import React, { useState, useEffect, useRef } from 'react';
import { Settings, X, Plus, Search } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import PromptList from './components/PromptList';
import PromptPreview from './components/PromptPreview';
import SettingsPanel from './components/SettingsPanel';
import PromptEditor from './components/PromptEditor';
import FolderDropZone from './components/FolderDropZone';

function App() {
  const [prompts, setPrompts] = useState([]);
  const [folders, setFolders] = useState([]);
  const [filteredPrompts, setFilteredPrompts] = useState([]);
  const [selectedPromptIndex, setSelectedPromptIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const [showEditor, setShowEditor] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState(null);
  const searchInputRef = useRef(null);

  // 调试日志
  useEffect(() => {
    console.log('🎨 App component mounted, checking styles...');
    console.log('📊 Current state:', {
      prompts: prompts.length,
      folders: folders.length,
      loading,
      searchQuery
    });

    // 检查CSS类是否正确应用
    setTimeout(() => {
      const appContainer = document.querySelector('.app-container');
      const headerContainer = document.querySelector('.header-container');
      const brandIcon = document.querySelector('.brand-icon');

      console.log('🔍 CSS Classes Check:', {
        appContainer: appContainer ? '✅ Found' : '❌ Missing',
        headerContainer: headerContainer ? '✅ Found' : '❌ Missing',
        brandIcon: brandIcon ? '✅ Found' : '❌ Missing',
        appContainerStyles: appContainer ? getComputedStyle(appContainer).background : 'N/A'
      });
    }, 100);
  }, [prompts.length, folders.length, loading, searchQuery]);

  // 初始化数据
  useEffect(() => {
    loadData();
    
    // 监听来自content script的消息
    window.addEventListener('message', handleContentMessage);
    
    return () => {
      window.removeEventListener('message', handleContentMessage);
    };
  }, []);

  // 当面板显示时，自动聚焦搜索框
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // 处理搜索
  useEffect(() => {
    filterPrompts(searchQuery);
  }, [prompts, searchQuery]);

  // 重置选中索引当过滤结果改变时
  useEffect(() => {
    setSelectedPromptIndex(0);
  }, [filteredPrompts]);

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true);

      // 获取prompts
      let promptsResponse, foldersResponse;

      try {
        promptsResponse = await sendMessageToBackground({ action: 'get-prompts' });
        foldersResponse = await sendMessageToBackground({ action: 'get-folders' });
      } catch (error) {
        console.log('🔧 Background script not available, using test data');
        // 如果background script不可用，使用测试数据
        promptsResponse = [
          {
            id: '1',
            title: '邮件开头',
            description: '专业的邮件问候语',
            content: '尊敬的[姓名]先生/女士：\n\n您好！',
            usageCount: 5,
            createdAt: Date.now() - 86400000,
            updatedAt: Date.now() - 3600000,
            lastUsedAt: Date.now() - 1800000
          },
          {
            id: '2',
            title: '会议邀请',
            description: '标准的会议邀请模板',
            content: '我们诚挚邀请您参加将于[时间]举行的[会议名称]。',
            usageCount: 3,
            createdAt: Date.now() - 172800000,
            updatedAt: Date.now() - 7200000
          }
        ];
        foldersResponse = [
          { id: 'f1', name: '无文件夹', order: 0 },
          { id: 'f2', name: '工作邮件', order: 1 }
        ];
      }

      setPrompts(promptsResponse || []);
      setFolders(foldersResponse || []);

    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 向background script发送消息
  const sendMessageToBackground = (message) => {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  };

  // 处理来自content script的消息
  const handleContentMessage = (event) => {
    
    switch (event.data.action) {
      case 'panel-shown':
        // 面板显示时，聚焦搜索框
        if (searchInputRef.current) {
          setTimeout(() => {
            searchInputRef.current.focus();
          }, 100);
        }
        break;
        
      default:
        break;
    }
  };

  // 过滤prompts
  const filterPrompts = (query) => {
    let filtered = [...prompts];

    // 按order字段排序，如果没有order字段则按创建时间排序
    filtered.sort((a, b) => {
      const orderA = typeof a.order === 'number' ? a.order : a.createdAt || 0;
      const orderB = typeof b.order === 'number' ? b.order : b.createdAt || 0;
      return orderA - orderB;
    });

    if (!query.trim()) {
      setFilteredPrompts(filtered);
      return;
    }

    const lowerQuery = query.toLowerCase();
    filtered = filtered.filter(prompt => {
      // 搜索标题、描述、内容
      const titleMatch = prompt.title.toLowerCase().includes(lowerQuery);
      const descriptionMatch = prompt.description?.toLowerCase().includes(lowerQuery);
      const contentMatch = prompt.content.toLowerCase().includes(lowerQuery);

      // 搜索文件夹名称
      let folderMatch = false;
      if (prompt.folderId) {
        const folder = folders.find(f => f.id === prompt.folderId);
        if (folder) {
          folderMatch = folder.name.toLowerCase().includes(lowerQuery);
        }
      }

      return titleMatch || descriptionMatch || contentMatch || folderMatch;
    });

    setFilteredPrompts(filtered);
  };

  // 处理键盘导航
  const handleKeyDown = (event) => {
    if (filteredPrompts.length === 0) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedPromptIndex(prev => 
          prev < filteredPrompts.length - 1 ? prev + 1 : 0
        );
        break;
        
      case 'ArrowUp':
        event.preventDefault();
        setSelectedPromptIndex(prev => 
          prev > 0 ? prev - 1 : filteredPrompts.length - 1
        );
        break;
        
      case 'Enter':
        event.preventDefault();
        if (filteredPrompts[selectedPromptIndex]) {
          insertPrompt(filteredPrompts[selectedPromptIndex]);
        }
        break;
        
      case 'Escape':
        event.preventDefault();
        hidePanel();
        break;
        
      default:
        break;
    }
  };

  // 插入prompt到页面
  const insertPrompt = (prompt) => {
    
    // 向content script发送消息
    window.parent.postMessage({
      action: 'insert-prompt',
      content: prompt.content
    }, '*');
    
    // 更新使用次数
    updatePromptUsage(prompt.id);
  };

  // 更新prompt使用次数
  const updatePromptUsage = async (promptId) => {
    try {
      const prompt = prompts.find(p => p.id === promptId);
      if (prompt) {
        const updatedPrompt = {
          ...prompt,
          usageCount: (prompt.usageCount || 0) + 1,
          lastUsedAt: Date.now()
        };
        
        await sendMessageToBackground({
          action: 'save-prompt',
          prompt: updatedPrompt
        });
        
        // 更新本地状态
        setPrompts(prev => prev.map(p => 
          p.id === promptId ? updatedPrompt : p
        ));
      }
    } catch (error) {
      console.error('Error updating prompt usage:', error);
    }
  };

  // 隐藏面板
  const hidePanel = () => {
    window.parent.postMessage({
      action: 'hide-panel'
    }, '*');
  };

  // 获取文件夹名称
  const getFolderName = (folderId) => {
    if (!folderId) return null;
    const folder = folders.find(f => f.id === folderId);
    return folder ? folder.name : null;
  };

  // 处理数据导入后的刷新
  const handleDataImported = () => {
    loadData();
  };

  // 显示设置面板
  const showSettingsPanel = () => {
    setShowSettings(true);
  };

  // 隐藏设置面板
  const hideSettingsPanel = () => {
    setShowSettings(false);
  };

  // 显示添加Prompt编辑器
  const showAddPromptEditor = () => {
    setEditingPrompt(null);
    setShowEditor(true);
  };

  // 显示编辑Prompt编辑器
  const showEditPromptEditor = (prompt) => {
    setEditingPrompt(prompt);
    setShowEditor(true);
  };

  // 隐藏编辑器
  const hideEditor = () => {
    setShowEditor(false);
    setEditingPrompt(null);
  };

  // 保存Prompt
  const savePrompt = async (promptData) => {
    try {
      const response = await sendMessageToBackground({
        action: 'save-prompt',
        prompt: promptData
      });

      if (response.success) {
        // 重新加载数据
        await loadData();
        hideEditor();
      } else {
        throw new Error(response.error || '保存失败');
      }
    } catch (error) {
      console.error('Error saving prompt:', error);
      throw error;
    }
  };

  // 删除Prompt
  const deletePrompt = async (promptId) => {
    try {
      const response = await sendMessageToBackground({
        action: 'delete-prompt',
        promptId
      });

      if (response.success) {
        // 重新加载数据
        await loadData();
        hideEditor();
      } else {
        throw new Error(response.error || '删除失败');
      }
    } catch (error) {
      console.error('Error deleting prompt:', error);
      throw error;
    }
  };

  // 处理Prompt移动（拖拽排序）
  const handlePromptMove = async (draggedPrompt, targetPrompt) => {
    try {
      // 获取当前显示的prompts列表
      const currentPrompts = [...filteredPrompts];

      // 找到拖拽的prompt和目标prompt的索引
      const draggedIndex = currentPrompts.findIndex(p => p.id === draggedPrompt.id);
      const targetIndex = currentPrompts.findIndex(p => p.id === targetPrompt.id);

      if (draggedIndex === -1 || targetIndex === -1) return;

      // 重新排列数组
      const newPrompts = [...currentPrompts];
      const [draggedItem] = newPrompts.splice(draggedIndex, 1);
      newPrompts.splice(targetIndex, 0, draggedItem);

      // 生成新的排序ID列表
      const promptIds = newPrompts.map(p => p.id);

      // 发送排序请求到background
      const response = await sendMessageToBackground({
        action: 'reorder-prompts',
        promptIds
      });

      if (response.success) {
        // 重新加载数据
        await loadData();
      } else {
        throw new Error(response.error || '排序失败');
      }

    } catch (error) {
      console.error('Error moving prompt:', error);
    }
  };

  // 处理Prompt移动到文件夹
  const handlePromptMoveToFolder = async (promptId, folderId) => {
    try {
      const response = await sendMessageToBackground({
        action: 'move-prompt-to-folder',
        promptId,
        folderId
      });

      if (response.success) {
        // 重新加载数据
        await loadData();
      } else {
        throw new Error(response.error || '移动失败');
      }
    } catch (error) {
      console.error('Error moving prompt to folder:', error);
    }
  };

  // 处理文件夹重排序
  const handleFolderReorder = async (draggedFolderId, targetFolderId) => {
    try {
      // 获取当前文件夹列表并按order排序
      const sortedFolders = [...folders].sort((a, b) => (a.order || 0) - (b.order || 0));

      // 找到拖拽的文件夹和目标文件夹的索引
      const draggedIndex = sortedFolders.findIndex(f => f.id === draggedFolderId);
      const targetIndex = sortedFolders.findIndex(f => f.id === targetFolderId);

      if (draggedIndex === -1 || targetIndex === -1) return;

      // 重新排列数组
      const newFolders = [...sortedFolders];
      const [draggedItem] = newFolders.splice(draggedIndex, 1);
      newFolders.splice(targetIndex, 0, draggedItem);

      // 生成新的排序ID列表
      const folderIds = newFolders.map(f => f.id);

      // 发送排序请求到background
      const response = await sendMessageToBackground({
        action: 'reorder-folders',
        folderIds
      });

      if (response.success) {
        // 重新加载数据
        await loadData();
      } else {
        throw new Error(response.error || '排序失败');
      }

    } catch (error) {
      console.error('Error reordering folders:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen app-container">
        <div className="text-center">
          <div className="w-12 h-12 brand-icon rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse">
            <span className="text-white text-lg font-bold">P</span>
          </div>
          <div className="text-slate-600 font-medium">加载中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col app-container text-foreground" onKeyDown={handleKeyDown} tabIndex={0}>
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-5 header-container">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 brand-icon rounded-lg flex items-center justify-center">
            <span className="text-white text-sm font-bold">P</span>
          </div>
          <h1 className="text-xl font-bold brand-title">
            Prompt 管理器
          </h1>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={showSettingsPanel}
            className="h-9 w-9 rounded-lg hover:bg-slate-100 transition-colors"
          >
            <Settings className="h-4 w-4 text-slate-600" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={hidePanel}
            className="h-9 w-9 rounded-lg hover:bg-slate-100 transition-colors"
          >
            <X className="h-4 w-4 text-slate-600" />
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="px-6 py-5 search-container">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            ref={searchInputRef}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="搜索 Prompt..."
            className="pl-12 h-11 search-input"
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Add Button */}
        <div className="px-6 py-4 border-b border-slate-200/60 bg-white/30">
          <Button
            onClick={showAddPromptEditor}
            className="w-full justify-start gap-3 h-12 add-button text-white font-medium"
          >
            <Plus className="h-5 w-5" />
            添加 Prompt
          </Button>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          <div className="flex-1 flex flex-col overflow-hidden">
            <PromptList
              prompts={filteredPrompts}
              selectedIndex={selectedPromptIndex}
              onSelect={setSelectedPromptIndex}
              onInsert={insertPrompt}
              getFolderName={getFolderName}
              searchQuery={searchQuery}
              onEdit={showEditPromptEditor}
              onDelete={deletePrompt}
              onPromptMove={handlePromptMove}
            />

            {filteredPrompts[selectedPromptIndex] && (
              <PromptPreview
                prompt={filteredPrompts[selectedPromptIndex]}
                folderName={getFolderName(filteredPrompts[selectedPromptIndex].folderId)}
              />
            )}
          </div>

          <div className="w-52 folder-sidebar overflow-y-auto">
            <FolderDropZone
              folders={folders}
              onPromptMoveToFolder={handlePromptMoveToFolder}
              onFolderReorder={handleFolderReorder}
            />
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="footer-container px-6 py-3">
        <div className="flex gap-6 text-xs text-slate-500">
          <span className="flex items-center gap-2">
            <kbd className="px-2 py-1 kbd-style text-xs font-mono">↑↓</kbd>
            <span className="font-medium">选择</span>
          </span>
          <span className="flex items-center gap-2">
            <kbd className="px-2 py-1 kbd-style text-xs font-mono">Enter</kbd>
            <span className="font-medium">插入</span>
          </span>
          <span className="flex items-center gap-2">
            <kbd className="px-2 py-1 kbd-style text-xs font-mono">Esc</kbd>
            <span className="font-medium">关闭</span>
          </span>
        </div>
      </div>

      {/* Modals */}
      {showSettings && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <SettingsPanel
            onClose={hideSettingsPanel}
            onDataImported={handleDataImported}
          />
        </div>
      )}

      {showEditor && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <PromptEditor
            prompt={editingPrompt}
            folders={folders}
            onSave={savePrompt}
            onCancel={hideEditor}
            onDelete={deletePrompt}
          />
        </div>
      )}
    </div>
  );
}

export default App;
