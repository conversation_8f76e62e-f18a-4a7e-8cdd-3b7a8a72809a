/*! For license information please see panel.js.LICENSE.txt */
(()=>{"use strict";var e={20:(e,t,n)=>{var r=n(540),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,l={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)o.call(t,r)&&!s.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:c,props:l,_owner:i.current}}t.Fragment=l,t.jsx=u,t.jsxs=u},287:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator,m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}function b(){}function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=v.prototype;var x=y.prototype=new b;x.constructor=y,h(x,v.prototype),x.isPureReactComponent=!0;var w=Array.isArray,k=Object.prototype.hasOwnProperty,S={current:null},N={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,r){var a,l={},o=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(o=""+t.key),t)k.call(t,a)&&!N.hasOwnProperty(a)&&(l[a]=t[a]);var s=arguments.length-2;if(1===s)l.children=r;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];l.children=u}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===l[a]&&(l[a]=s[a]);return{$$typeof:n,type:e,key:o,ref:i,props:l,_owner:S.current}}function C(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var j=/\/+/g;function _(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function z(e,t,a,l,o){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s=!1;if(null===e)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return o=o(s=e),e=""===l?"."+_(s,0):l,w(o)?(a="",null!=e&&(a=e.replace(j,"$&/")+"/"),z(o,t,a,"",function(e){return e})):null!=o&&(C(o)&&(o=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(o,a+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(j,"$&/")+"/")+e)),t.push(o)),1;if(s=0,l=""===l?".":l+":",w(e))for(var u=0;u<e.length;u++){var c=l+_(i=e[u],u);s+=z(i,t,a,c,o)}else if(c=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"==typeof c)for(e=c.call(e),u=0;!(i=e.next()).done;)s+=z(i=i.value,t,a,c=l+_(i,u++),o);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function P(e,t,n){if(null==e)return e;var r=[],a=0;return z(e,r,"","",function(e){return t.call(n,e,a++)}),r}function T(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L={current:null},D={transition:null},M={ReactCurrentDispatcher:L,ReactCurrentBatchConfig:D,ReactCurrentOwner:S};function R(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:P,forEach:function(e,t,n){P(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return P(e,function(){t++}),t},toArray:function(e){return P(e,function(e){return e})||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=o,t.PureComponent=y,t.StrictMode=l,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=M,t.act=R,t.cloneElement=function(e,t,r){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=h({},e.props),l=e.key,o=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(o=t.ref,i=S.current),void 0!==t.key&&(l=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)k.call(t,u)&&!N.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];a.children=s}return{$$typeof:n,type:e.type,key:l,ref:o,props:a,_owner:i}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:T}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=D.transition;D.transition={};try{e()}finally{D.transition=t}},t.unstable_act=R,t.useCallback=function(e,t){return L.current.useCallback(e,t)},t.useContext=function(e){return L.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return L.current.useDeferredValue(e)},t.useEffect=function(e,t){return L.current.useEffect(e,t)},t.useId=function(){return L.current.useId()},t.useImperativeHandle=function(e,t,n){return L.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return L.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return L.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return L.current.useMemo(e,t)},t.useReducer=function(e,t,n){return L.current.useReducer(e,t,n)},t.useRef=function(e){return L.current.useRef(e)},t.useState=function(e){return L.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return L.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return L.current.useTransition()},t.version="18.3.1"},338:(e,t,n)=>{var r=n(961);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},463:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var i=2*(r+1)-1,s=e[i],u=i+1,c=e[u];if(0>l(s,n))u<a&&0>l(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[i]=n,r=i);else{if(!(u<a&&0>l(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var u=[],c=[],d=1,f=null,p=3,m=!1,h=!1,g=!1,v="function"==typeof setTimeout?setTimeout:null,b="function"==typeof clearTimeout?clearTimeout:null,y="undefined"!=typeof setImmediate?setImmediate:null;function x(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function w(e){if(g=!1,x(e),!h)if(null!==r(u))h=!0,D(k);else{var t=r(c);null!==t&&M(w,t.startTime-e)}}function k(e,n){h=!1,g&&(g=!1,b(C),C=-1),m=!0;var l=p;try{for(x(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!z());){var o=f.callback;if("function"==typeof o){f.callback=null,p=f.priorityLevel;var i=o(f.expirationTime<=n);n=t.unstable_now(),"function"==typeof i?f.callback=i:f===r(u)&&a(u),x(n)}else a(u);f=r(u)}if(null!==f)var s=!0;else{var d=r(c);null!==d&&M(w,d.startTime-n),s=!1}return s}finally{f=null,p=l,m=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,N=!1,E=null,C=-1,j=5,_=-1;function z(){return!(t.unstable_now()-_<j)}function P(){if(null!==E){var e=t.unstable_now();_=e;var n=!0;try{n=E(!0,e)}finally{n?S():(N=!1,E=null)}}else N=!1}if("function"==typeof y)S=function(){y(P)};else if("undefined"!=typeof MessageChannel){var T=new MessageChannel,L=T.port2;T.port1.onmessage=P,S=function(){L.postMessage(null)}}else S=function(){v(P,0)};function D(e){E=e,N||(N=!0,S())}function M(e,n){C=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){h||m||(h=!0,D(k))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,l){var o=t.unstable_now();switch(l="object"==typeof l&&null!==l&&"number"==typeof(l=l.delay)&&0<l?o+l:o,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>o?(e.sortIndex=l,n(c,e),null===r(u)&&e===r(c)&&(g?(b(C),C=-1):g=!0,M(w,l-o))):(e.sortIndex=i,n(u,e),h||m||(h=!0,D(k))),e},t.unstable_shouldYield=z,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},540:(e,t,n)=>{e.exports=n(287)},551:(e,t,n)=>{var r=n(540),a=n(982);function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var o=new Set,i={};function s(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(i[e]=t,e=0;e<t.length;e++)o.add(t[e])}var c=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},m={};function h(e,t,n,r,a,l,o){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=o}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new h(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new h(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new h(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new h(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new h(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new h(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new h(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new h(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new h(e,5,!1,e.toLowerCase(),null,!1,!1)});var v=/[\-:]([a-z])/g;function b(e){return e[1].toUpperCase()}function y(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null==t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(m,e)||!d.call(p,e)&&(f.test(e)?m[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(v,b);g[t]=new h(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(v,b);g[t]=new h(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(v,b);g[t]=new h(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new h("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!0,!0)});var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),k=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),N=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),j=Symbol.for("react.context"),_=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),P=Symbol.for("react.suspense_list"),T=Symbol.for("react.memo"),L=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var D=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var M=Symbol.iterator;function R(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=M&&e[M]||e["@@iterator"])?e:null}var I,O=Object.assign;function F(e){if(void 0===I)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);I=t&&t[1]||""}return"\n"+I+e}var U=!1;function A(e,t){if(!e||U)return"";U=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(e){var r=e}Reflect.construct(e,[],t)}else{try{t.call()}catch(e){r=e}e.call(t.prototype)}else{try{throw Error()}catch(e){r=e}e()}}catch(t){if(t&&r&&"string"==typeof t.stack){for(var a=t.stack.split("\n"),l=r.stack.split("\n"),o=a.length-1,i=l.length-1;1<=o&&0<=i&&a[o]!==l[i];)i--;for(;1<=o&&0<=i;o--,i--)if(a[o]!==l[i]){if(1!==o||1!==i)do{if(o--,0>--i||a[o]!==l[i]){var s="\n"+a[o].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=o&&0<=i);break}}}finally{U=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?F(e):""}function $(e){switch(e.tag){case 5:return F(e.type);case 16:return F("Lazy");case 13:return F("Suspense");case 19:return F("SuspenseList");case 0:case 2:case 15:return A(e.type,!1);case 11:return A(e.type.render,!1);case 1:return A(e.type,!0);default:return""}}function V(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case S:return"Fragment";case k:return"Portal";case E:return"Profiler";case N:return"StrictMode";case z:return"Suspense";case P:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case j:return(e.displayName||"Context")+".Consumer";case C:return(e._context.displayName||"Context")+".Provider";case _:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case T:return null!==(t=e.displayName||null)?t:V(e.type)||"Memo";case L:t=e._payload,e=e._init;try{return V(e(t))}catch(e){}}return null}function B(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return V(t);case 8:return t===N?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}function W(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function H(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Q(e){e._valueTracker||(e._valueTracker=function(e){var t=H(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=H(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function G(e,t){var n=t.checked;return O({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Y(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=W(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function X(e,t){null!=(t=t.checked)&&y(e,"checked",t,!1)}function Z(e,t){X(e,t);var n=W(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,W(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+W(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(l(91));return O({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(l(92));if(te(n)){if(1<n.length)throw Error(l(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:W(n)}}function le(e,t){var n=W(t.value),r=W(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function oe(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ie(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ie(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ce(e,t)})}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},me=["Webkit","ms","Moz","O"];function he(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=he(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach(function(e){me.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]})});var ve=O({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function be(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(l(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(l(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(l(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(l(62))}}function ye(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var ke=null,Se=null,Ne=null;function Ee(e){if(e=ya(e)){if("function"!=typeof ke)throw Error(l(280));var t=e.stateNode;t&&(t=wa(t),ke(e.stateNode,e.type,t))}}function Ce(e){Se?Ne?Ne.push(e):Ne=[e]:Se=e}function je(){if(Se){var e=Se,t=Ne;if(Ne=Se=null,Ee(e),t)for(e=0;e<t.length;e++)Ee(t[e])}}function _e(e,t){return e(t)}function ze(){}var Pe=!1;function Te(e,t,n){if(Pe)return e(t,n);Pe=!0;try{return _e(e,t,n)}finally{Pe=!1,(null!==Se||null!==Ne)&&(ze(),je())}}function Le(e,t){var n=e.stateNode;if(null===n)return null;var r=wa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(l(231,t,typeof n));return n}var De=!1;if(c)try{var Me={};Object.defineProperty(Me,"passive",{get:function(){De=!0}}),window.addEventListener("test",Me,Me),window.removeEventListener("test",Me,Me)}catch(ce){De=!1}function Re(e,t,n,r,a,l,o,i,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(e){this.onError(e)}}var Ie=!1,Oe=null,Fe=!1,Ue=null,Ae={onError:function(e){Ie=!0,Oe=e}};function $e(e,t,n,r,a,l,o,i,s){Ie=!1,Oe=null,Re.apply(Ae,arguments)}function Ve(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Be(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&null!==(e=e.alternate)&&(t=e.memoizedState),null!==t)return t.dehydrated}return null}function We(e){if(Ve(e)!==e)throw Error(l(188))}function He(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ve(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return We(a),e;if(o===r)return We(a),t;o=o.sibling}throw Error(l(188))}if(n.return!==r.return)n=a,r=o;else{for(var i=!1,s=a.child;s;){if(s===n){i=!0,n=a,r=o;break}if(s===r){i=!0,r=a,n=o;break}s=s.sibling}if(!i){for(s=o.child;s;){if(s===n){i=!0,n=o,r=a;break}if(s===r){i=!0,r=o,n=a;break}s=s.sibling}if(!i)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(e))?Qe(e):null}function Qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Qe(e);if(null!==t)return t;e=e.sibling}return null}var qe=a.unstable_scheduleCallback,Ke=a.unstable_cancelCallback,Ge=a.unstable_shouldYield,Ye=a.unstable_requestPaint,Xe=a.unstable_now,Ze=a.unstable_getCurrentPriorityLevel,Je=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,lt=null,ot=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(it(e)/st|0)|0},it=Math.log,st=Math.LN2,ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,l=e.pingedLanes,o=268435455&n;if(0!==o){var i=o&~a;0!==i?r=dt(i):0!==(l&=o)&&(r=dt(l))}else 0!==(o=n&~a)?r=dt(o):0!==l&&(r=dt(l));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(l=t&-t)||16===a&&4194240&l))return t;if(4&r&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-ot(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function mt(e){return 0!=(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ht(){var e=ut;return!(4194240&(ut<<=1))&&(ut=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-ot(t)]=n}function bt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var yt=0;function xt(e){return 1<(e&=-e)?4<e?268435455&e?16:536870912:4:1}var wt,kt,St,Nt,Et,Ct=!1,jt=[],_t=null,zt=null,Pt=null,Tt=new Map,Lt=new Map,Dt=[],Mt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Rt(e,t){switch(e){case"focusin":case"focusout":_t=null;break;case"dragenter":case"dragleave":zt=null;break;case"mouseover":case"mouseout":Pt=null;break;case"pointerover":case"pointerout":Tt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Lt.delete(t.pointerId)}}function It(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&null!==(t=ya(t))&&kt(t),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Ot(e){var t=ba(e.target);if(null!==t){var n=Ve(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Be(n)))return e.blockedOn=t,void Et(e.priority,function(){St(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Ft(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Gt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ya(n))&&kt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function Ut(e,t,n){Ft(e)&&n.delete(t)}function At(){Ct=!1,null!==_t&&Ft(_t)&&(_t=null),null!==zt&&Ft(zt)&&(zt=null),null!==Pt&&Ft(Pt)&&(Pt=null),Tt.forEach(Ut),Lt.forEach(Ut)}function $t(e,t){e.blockedOn===t&&(e.blockedOn=null,Ct||(Ct=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,At)))}function Vt(e){function t(t){return $t(t,e)}if(0<jt.length){$t(jt[0],e);for(var n=1;n<jt.length;n++){var r=jt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==_t&&$t(_t,e),null!==zt&&$t(zt,e),null!==Pt&&$t(Pt,e),Tt.forEach(t),Lt.forEach(t),n=0;n<Dt.length;n++)(r=Dt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Dt.length&&null===(n=Dt[0]).blockedOn;)Ot(n),null===n.blockedOn&&Dt.shift()}var Bt=x.ReactCurrentBatchConfig,Wt=!0;function Ht(e,t,n,r){var a=yt,l=Bt.transition;Bt.transition=null;try{yt=1,qt(e,t,n,r)}finally{yt=a,Bt.transition=l}}function Qt(e,t,n,r){var a=yt,l=Bt.transition;Bt.transition=null;try{yt=4,qt(e,t,n,r)}finally{yt=a,Bt.transition=l}}function qt(e,t,n,r){if(Wt){var a=Gt(e,t,n,r);if(null===a)Wr(e,t,r,Kt,n),Rt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return _t=It(_t,e,t,n,r,a),!0;case"dragenter":return zt=It(zt,e,t,n,r,a),!0;case"mouseover":return Pt=It(Pt,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return Tt.set(l,It(Tt.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,Lt.set(l,It(Lt.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Rt(e,r),4&t&&-1<Mt.indexOf(e)){for(;null!==a;){var l=ya(a);if(null!==l&&wt(l),null===(l=Gt(e,t,n,r))&&Wr(e,t,r,Kt,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Wr(e,t,r,null,n)}}var Kt=null;function Gt(e,t,n,r){if(Kt=null,null!==(e=ba(e=we(r))))if(null===(t=Ve(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Be(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Yt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case Je:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Xt=null,Zt=null,Jt=null;function en(){if(Jt)return Jt;var e,t,n=Zt,r=n.length,a="value"in Xt?Xt.value:Xt.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[l-t];t++);return Jt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,l){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return O(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var ln,on,sn,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(un),dn=O({},un,{view:0,detail:0}),fn=an(dn),pn=O({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:En,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(ln=e.screenX-sn.screenX,on=e.screenY-sn.screenY):on=ln=0,sn=e),ln)},movementY:function(e){return"movementY"in e?e.movementY:on}}),mn=an(pn),hn=an(O({},pn,{dataTransfer:0})),gn=an(O({},dn,{relatedTarget:0})),vn=an(O({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),bn=O({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),yn=an(bn),xn=an(O({},un,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Nn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sn[e])&&!!t[e]}function En(){return Nn}var Cn=O({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?kn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:En,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),jn=an(Cn),_n=an(O({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),zn=an(O({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:En})),Pn=an(O({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),Tn=O({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ln=an(Tn),Dn=[9,13,27,32],Mn=c&&"CompositionEvent"in window,Rn=null;c&&"documentMode"in document&&(Rn=document.documentMode);var In=c&&"TextEvent"in window&&!Rn,On=c&&(!Mn||Rn&&8<Rn&&11>=Rn),Fn=String.fromCharCode(32),Un=!1;function An(e,t){switch(e){case"keyup":return-1!==Dn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $n(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Vn=!1,Bn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Bn[e.type]:"textarea"===t}function Hn(e,t,n,r){Ce(r),0<(t=Qr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Qn=null,qn=null;function Kn(e){Fr(e,0)}function Gn(e){if(q(xa(e)))return e}function Yn(e,t){if("change"===e)return t}var Xn=!1;if(c){var Zn;if(c){var Jn="oninput"in document;if(!Jn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Jn="function"==typeof er.oninput}Zn=Jn}else Zn=!1;Xn=Zn&&(!document.documentMode||9<document.documentMode)}function tr(){Qn&&(Qn.detachEvent("onpropertychange",nr),qn=Qn=null)}function nr(e){if("value"===e.propertyName&&Gn(qn)){var t=[];Hn(t,qn,e,we(e)),Te(Kn,t)}}function rr(e,t,n){"focusin"===e?(tr(),qn=n,(Qn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Gn(qn)}function lr(e,t){if("click"===e)return Gn(t)}function or(e,t){if("input"===e||"change"===e)return Gn(t)}var ir="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function sr(e,t){if(ir(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!ir(e[a],t[a]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(e){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function mr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,l=Math.min(r.start,a);r=void 0===r.end?l:Math.min(r.end,a),!e.extend&&l>r&&(a=r,r=l,l=a),a=cr(n,l);var o=cr(n,r);a&&o&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"==typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var hr=c&&"documentMode"in document&&11>=document.documentMode,gr=null,vr=null,br=null,yr=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;yr||null==gr||gr!==K(r)||(r="selectionStart"in(r=gr)&&pr(r)?{start:r.selectionStart,end:r.selectionEnd}:{anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},br&&sr(br,r)||(br=r,0<(r=Qr(vr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},Sr={},Nr={};function Er(e){if(Sr[e])return Sr[e];if(!kr[e])return e;var t,n=kr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Nr)return Sr[e]=n[t];return e}c&&(Nr=document.createElement("div").style,"AnimationEvent"in window||(delete kr.animationend.animation,delete kr.animationiteration.animation,delete kr.animationstart.animation),"TransitionEvent"in window||delete kr.transitionend.transition);var Cr=Er("animationend"),jr=Er("animationiteration"),_r=Er("animationstart"),zr=Er("transitionend"),Pr=new Map,Tr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Lr(e,t){Pr.set(e,t),s(t,[e])}for(var Dr=0;Dr<Tr.length;Dr++){var Mr=Tr[Dr];Lr(Mr.toLowerCase(),"on"+(Mr[0].toUpperCase()+Mr.slice(1)))}Lr(Cr,"onAnimationEnd"),Lr(jr,"onAnimationIteration"),Lr(_r,"onAnimationStart"),Lr("dblclick","onDoubleClick"),Lr("focusin","onFocus"),Lr("focusout","onBlur"),Lr(zr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Rr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ir=new Set("cancel close invalid load scroll toggle".split(" ").concat(Rr));function Or(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,o,i,s,u){if($e.apply(this,arguments),Ie){if(!Ie)throw Error(l(198));var c=Oe;Ie=!1,Oe=null,Fe||(Fe=!0,Ue=c)}}(r,t,void 0,e),e.currentTarget=null}function Fr(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==l&&a.isPropagationStopped())break e;Or(a,i,u),l=s}else for(o=0;o<r.length;o++){if(s=(i=r[o]).instance,u=i.currentTarget,i=i.listener,s!==l&&a.isPropagationStopped())break e;Or(a,i,u),l=s}}}if(Fe)throw e=Ue,Fe=!1,Ue=null,e}function Ur(e,t){var n=t[ha];void 0===n&&(n=t[ha]=new Set);var r=e+"__bubble";n.has(r)||(Br(t,e,2,!1),n.add(r))}function Ar(e,t,n){var r=0;t&&(r|=4),Br(n,e,r,t)}var $r="_reactListening"+Math.random().toString(36).slice(2);function Vr(e){if(!e[$r]){e[$r]=!0,o.forEach(function(t){"selectionchange"!==t&&(Ir.has(t)||Ar(t,!1,e),Ar(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[$r]||(t[$r]=!0,Ar("selectionchange",!1,t))}}function Br(e,t,n,r){switch(Yt(t)){case 1:var a=Ht;break;case 4:a=Qt;break;default:a=qt}n=a.bind(null,t,n,e),a=void 0,!De||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Wr(e,t,n,r,a){var l=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var i=r.stateNode.containerInfo;if(i===a||8===i.nodeType&&i.parentNode===a)break;if(4===o)for(o=r.return;null!==o;){var s=o.tag;if((3===s||4===s)&&((s=o.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;o=o.return}for(;null!==i;){if(null===(o=ba(i)))return;if(5===(s=o.tag)||6===s){r=l=o;continue e}i=i.parentNode}}r=r.return}Te(function(){var r=l,a=we(n),o=[];e:{var i=Pr.get(e);if(void 0!==i){var s=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=jn;break;case"focusin":u="focus",s=gn;break;case"focusout":u="blur",s=gn;break;case"beforeblur":case"afterblur":s=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=mn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=hn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=zn;break;case Cr:case jr:case _r:s=vn;break;case zr:s=Pn;break;case"scroll":s=fn;break;case"wheel":s=Ln;break;case"copy":case"cut":case"paste":s=yn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=_n}var c=!!(4&t),d=!c&&"scroll"===e,f=c?null!==i?i+"Capture":null:i;c=[];for(var p,m=r;null!==m;){var h=(p=m).stateNode;if(5===p.tag&&null!==h&&(p=h,null!==f&&null!=(h=Le(m,f))&&c.push(Hr(m,h,p))),d)break;m=m.return}0<c.length&&(i=new s(i,u,null,n,a),o.push({event:i,listeners:c}))}}if(!(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===xe||!(u=n.relatedTarget||n.fromElement)||!ba(u)&&!u[ma])&&(s||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?ba(u):null)&&(u!==(d=Ve(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=mn,h="onMouseLeave",f="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(c=_n,h="onPointerLeave",f="onPointerEnter",m="pointer"),d=null==s?i:xa(s),p=null==u?i:xa(u),(i=new c(h,m+"leave",s,n,a)).target=d,i.relatedTarget=p,h=null,ba(a)===r&&((c=new c(f,m+"enter",u,n,a)).target=p,c.relatedTarget=d,h=c),d=h,s&&u)e:{for(f=u,m=0,p=c=s;p;p=qr(p))m++;for(p=0,h=f;h;h=qr(h))p++;for(;0<m-p;)c=qr(c),m--;for(;0<p-m;)f=qr(f),p--;for(;m--;){if(c===f||null!==f&&c===f.alternate)break e;c=qr(c),f=qr(f)}c=null}else c=null;null!==s&&Kr(o,i,s,c,!1),null!==u&&null!==d&&Kr(o,d,u,c,!0)}if("select"===(s=(i=r?xa(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===s&&"file"===i.type)var g=Yn;else if(Wn(i))if(Xn)g=or;else{g=ar;var v=rr}else(s=i.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(g=lr);switch(g&&(g=g(e,r))?Hn(o,g,n,a):(v&&v(e,i,r),"focusout"===e&&(v=i._wrapperState)&&v.controlled&&"number"===i.type&&ee(i,"number",i.value)),v=r?xa(r):window,e){case"focusin":(Wn(v)||"true"===v.contentEditable)&&(gr=v,vr=r,br=null);break;case"focusout":br=vr=gr=null;break;case"mousedown":yr=!0;break;case"contextmenu":case"mouseup":case"dragend":yr=!1,xr(o,n,a);break;case"selectionchange":if(hr)break;case"keydown":case"keyup":xr(o,n,a)}var b;if(Mn)e:{switch(e){case"compositionstart":var y="onCompositionStart";break e;case"compositionend":y="onCompositionEnd";break e;case"compositionupdate":y="onCompositionUpdate";break e}y=void 0}else Vn?An(e,n)&&(y="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(y="onCompositionStart");y&&(On&&"ko"!==n.locale&&(Vn||"onCompositionStart"!==y?"onCompositionEnd"===y&&Vn&&(b=en()):(Zt="value"in(Xt=a)?Xt.value:Xt.textContent,Vn=!0)),0<(v=Qr(r,y)).length&&(y=new xn(y,e,null,n,a),o.push({event:y,listeners:v}),(b||null!==(b=$n(n)))&&(y.data=b))),(b=In?function(e,t){switch(e){case"compositionend":return $n(t);case"keypress":return 32!==t.which?null:(Un=!0,Fn);case"textInput":return(e=t.data)===Fn&&Un?null:e;default:return null}}(e,n):function(e,t){if(Vn)return"compositionend"===e||!Mn&&An(e,t)?(e=en(),Jt=Zt=Xt=null,Vn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return On&&"ko"!==t.locale?null:t.data}}(e,n))&&0<(r=Qr(r,"onBeforeInput")).length&&(a=new xn("onBeforeInput","beforeinput",null,n,a),o.push({event:a,listeners:r}),a.data=b)}Fr(o,t)})}function Hr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;5===a.tag&&null!==l&&(a=l,null!=(l=Le(e,n))&&r.unshift(Hr(e,l,a)),null!=(l=Le(e,t))&&r.push(Hr(e,l,a))),e=e.return}return r}function qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,a){for(var l=t._reactName,o=[];null!==n&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(null!==s&&s===r)break;5===i.tag&&null!==u&&(i=u,a?null!=(s=Le(n,l))&&o.unshift(Hr(n,s,i)):a||null!=(s=Le(n,l))&&o.push(Hr(n,s,i))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Gr=/\r\n?/g,Yr=/\u0000|\uFFFD/g;function Xr(e){return("string"==typeof e?e:""+e).replace(Gr,"\n").replace(Yr,"")}function Zr(e,t,n){if(t=Xr(t),Xr(e)!==t&&n)throw Error(l(425))}function Jr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"==typeof setTimeout?setTimeout:void 0,aa="function"==typeof clearTimeout?clearTimeout:void 0,la="function"==typeof Promise?Promise:void 0,oa="function"==typeof queueMicrotask?queueMicrotask:void 0!==la?function(e){return la.resolve(null).then(e).catch(ia)}:ra;function ia(e){setTimeout(function(){throw e})}function sa(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Vt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Vt(t)}function ua(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ca(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ma="__reactContainer$"+da,ha="__reactEvents$"+da,ga="__reactListeners$"+da,va="__reactHandles$"+da;function ba(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ma]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ca(e);null!==e;){if(n=e[fa])return n;e=ca(e)}return t}n=(e=n).parentNode}return null}function ya(e){return!(e=e[fa]||e[ma])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(l(33))}function wa(e){return e[pa]||null}var ka=[],Sa=-1;function Na(e){return{current:e}}function Ea(e){0>Sa||(e.current=ka[Sa],ka[Sa]=null,Sa--)}function Ca(e,t){Sa++,ka[Sa]=e.current,e.current=t}var ja={},_a=Na(ja),za=Na(!1),Pa=ja;function Ta(e,t){var n=e.type.contextTypes;if(!n)return ja;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,l={};for(a in n)l[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function La(e){return null!=e.childContextTypes}function Da(){Ea(za),Ea(_a)}function Ma(e,t,n){if(_a.current!==ja)throw Error(l(168));Ca(_a,t),Ca(za,n)}function Ra(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(l(108,B(e)||"Unknown",a));return O({},n,r)}function Ia(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ja,Pa=_a.current,Ca(_a,e),Ca(za,za.current),!0}function Oa(e,t,n){var r=e.stateNode;if(!r)throw Error(l(169));n?(e=Ra(e,t,Pa),r.__reactInternalMemoizedMergedChildContext=e,Ea(za),Ea(_a),Ca(_a,e)):Ea(za),Ca(za,n)}var Fa=null,Ua=!1,Aa=!1;function $a(e){null===Fa?Fa=[e]:Fa.push(e)}function Va(){if(!Aa&&null!==Fa){Aa=!0;var e=0,t=yt;try{var n=Fa;for(yt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Fa=null,Ua=!1}catch(t){throw null!==Fa&&(Fa=Fa.slice(e+1)),qe(Je,Va),t}finally{yt=t,Aa=!1}}return null}var Ba=[],Wa=0,Ha=null,Qa=0,qa=[],Ka=0,Ga=null,Ya=1,Xa="";function Za(e,t){Ba[Wa++]=Qa,Ba[Wa++]=Ha,Ha=e,Qa=t}function Ja(e,t,n){qa[Ka++]=Ya,qa[Ka++]=Xa,qa[Ka++]=Ga,Ga=e;var r=Ya;e=Xa;var a=32-ot(r)-1;r&=~(1<<a),n+=1;var l=32-ot(t)+a;if(30<l){var o=a-a%5;l=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Ya=1<<32-ot(t)+a|n<<a|r,Xa=l+e}else Ya=1<<l|n<<a|r,Xa=e}function el(e){null!==e.return&&(Za(e,1),Ja(e,1,0))}function tl(e){for(;e===Ha;)Ha=Ba[--Wa],Ba[Wa]=null,Qa=Ba[--Wa],Ba[Wa]=null;for(;e===Ga;)Ga=qa[--Ka],qa[Ka]=null,Xa=qa[--Ka],qa[Ka]=null,Ya=qa[--Ka],qa[Ka]=null}var nl=null,rl=null,al=!1,ll=null;function ol(e,t){var n=Tu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function il(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,nl=e,rl=ua(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,nl=e,rl=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ga?{id:Ya,overflow:Xa}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Tu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,nl=e,rl=null,!0);default:return!1}}function sl(e){return!(!(1&e.mode)||128&e.flags)}function ul(e){if(al){var t=rl;if(t){var n=t;if(!il(e,t)){if(sl(e))throw Error(l(418));t=ua(n.nextSibling);var r=nl;t&&il(e,t)?ol(r,n):(e.flags=-4097&e.flags|2,al=!1,nl=e)}}else{if(sl(e))throw Error(l(418));e.flags=-4097&e.flags|2,al=!1,nl=e}}}function cl(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;nl=e}function dl(e){if(e!==nl)return!1;if(!al)return cl(e),al=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=rl)){if(sl(e))throw fl(),Error(l(418));for(;t;)ol(e,t),t=ua(t.nextSibling)}if(cl(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){rl=ua(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}rl=null}}else rl=nl?ua(e.stateNode.nextSibling):null;return!0}function fl(){for(var e=rl;e;)e=ua(e.nextSibling)}function pl(){rl=nl=null,al=!1}function ml(e){null===ll?ll=[e]:ll.push(e)}var hl=x.ReactCurrentBatchConfig;function gl(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(l(309));var r=n.stateNode}if(!r)throw Error(l(147,e));var a=r,o=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===o?t.ref:(t=function(e){var t=a.refs;null===e?delete t[o]:t[o]=e},t._stringRef=o,t)}if("string"!=typeof e)throw Error(l(284));if(!n._owner)throw Error(l(290,e))}return e}function vl(e,t){throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bl(e){return(0,e._init)(e._payload)}function yl(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Du(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Ou(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var l=n.type;return l===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"==typeof l&&null!==l&&l.$$typeof===L&&bl(l)===t.type)?((r=a(t,n.props)).ref=gl(e,t,n),r.return=e,r):((r=Mu(n.type,n.key,n.props,null,e.mode,r)).ref=gl(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Fu(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=Ru(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=Ou(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Mu(t.type,t.key,t.props,null,e.mode,n)).ref=gl(e,null,t),n.return=e,n;case k:return(t=Fu(t,e.mode,n)).return=e,t;case L:return f(e,(0,t._init)(t._payload),n)}if(te(t)||R(t))return(t=Ru(t,e.mode,n,null)).return=e,t;vl(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==a?null:s(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?u(e,t,n,r):null;case k:return n.key===a?c(e,t,n,r):null;case L:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||R(n))return null!==a?null:d(e,t,n,r,null);vl(e,n)}return null}function m(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case k:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case L:return m(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||R(r))return d(t,e=e.get(n)||null,r,a,null);vl(t,r)}return null}function h(a,l,i,s){for(var u=null,c=null,d=l,h=l=0,g=null;null!==d&&h<i.length;h++){d.index>h?(g=d,d=null):g=d.sibling;var v=p(a,d,i[h],s);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(a,d),l=o(v,l,h),null===c?u=v:c.sibling=v,c=v,d=g}if(h===i.length)return n(a,d),al&&Za(a,h),u;if(null===d){for(;h<i.length;h++)null!==(d=f(a,i[h],s))&&(l=o(d,l,h),null===c?u=d:c.sibling=d,c=d);return al&&Za(a,h),u}for(d=r(a,d);h<i.length;h++)null!==(g=m(d,a,h,i[h],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?h:g.key),l=o(g,l,h),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach(function(e){return t(a,e)}),al&&Za(a,h),u}function g(a,i,s,u){var c=R(s);if("function"!=typeof c)throw Error(l(150));if(null==(s=c.call(s)))throw Error(l(151));for(var d=c=null,h=i,g=i=0,v=null,b=s.next();null!==h&&!b.done;g++,b=s.next()){h.index>g?(v=h,h=null):v=h.sibling;var y=p(a,h,b.value,u);if(null===y){null===h&&(h=v);break}e&&h&&null===y.alternate&&t(a,h),i=o(y,i,g),null===d?c=y:d.sibling=y,d=y,h=v}if(b.done)return n(a,h),al&&Za(a,g),c;if(null===h){for(;!b.done;g++,b=s.next())null!==(b=f(a,b.value,u))&&(i=o(b,i,g),null===d?c=b:d.sibling=b,d=b);return al&&Za(a,g),c}for(h=r(a,h);!b.done;g++,b=s.next())null!==(b=m(h,a,g,b.value,u))&&(e&&null!==b.alternate&&h.delete(null===b.key?g:b.key),i=o(b,i,g),null===d?c=b:d.sibling=b,d=b);return e&&h.forEach(function(e){return t(a,e)}),al&&Za(a,g),c}return function e(r,l,o,s){if("object"==typeof o&&null!==o&&o.type===S&&null===o.key&&(o=o.props.children),"object"==typeof o&&null!==o){switch(o.$$typeof){case w:e:{for(var u=o.key,c=l;null!==c;){if(c.key===u){if((u=o.type)===S){if(7===c.tag){n(r,c.sibling),(l=a(c,o.props.children)).return=r,r=l;break e}}else if(c.elementType===u||"object"==typeof u&&null!==u&&u.$$typeof===L&&bl(u)===c.type){n(r,c.sibling),(l=a(c,o.props)).ref=gl(r,c,o),l.return=r,r=l;break e}n(r,c);break}t(r,c),c=c.sibling}o.type===S?((l=Ru(o.props.children,r.mode,s,o.key)).return=r,r=l):((s=Mu(o.type,o.key,o.props,null,r.mode,s)).ref=gl(r,l,o),s.return=r,r=s)}return i(r);case k:e:{for(c=o.key;null!==l;){if(l.key===c){if(4===l.tag&&l.stateNode.containerInfo===o.containerInfo&&l.stateNode.implementation===o.implementation){n(r,l.sibling),(l=a(l,o.children||[])).return=r,r=l;break e}n(r,l);break}t(r,l),l=l.sibling}(l=Fu(o,r.mode,s)).return=r,r=l}return i(r);case L:return e(r,l,(c=o._init)(o._payload),s)}if(te(o))return h(r,l,o,s);if(R(o))return g(r,l,o,s);vl(r,o)}return"string"==typeof o&&""!==o||"number"==typeof o?(o=""+o,null!==l&&6===l.tag?(n(r,l.sibling),(l=a(l,o)).return=r,r=l):(n(r,l),(l=Ou(o,r.mode,s)).return=r,r=l),i(r)):n(r,l)}}var xl=yl(!0),wl=yl(!1),kl=Na(null),Sl=null,Nl=null,El=null;function Cl(){El=Nl=Sl=null}function jl(e){var t=kl.current;Ea(kl),e._currentValue=t}function _l(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function zl(e,t){Sl=e,El=Nl=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(yi=!0),e.firstContext=null)}function Pl(e){var t=e._currentValue;if(El!==e)if(e={context:e,memoizedValue:t,next:null},null===Nl){if(null===Sl)throw Error(l(308));Nl=e,Sl.dependencies={lanes:0,firstContext:e}}else Nl=Nl.next=e;return t}var Tl=null;function Ll(e){null===Tl?Tl=[e]:Tl.push(e)}function Dl(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Ll(t)):(n.next=a.next,a.next=n),t.interleaved=n,Ml(e,r)}function Ml(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Rl=!1;function Il(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ol(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Fl(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ul(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&_s){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Ml(e,n)}return null===(a=r.interleaved)?(t.next=t,Ll(r)):(t.next=a.next,a.next=t),r.interleaved=t,Ml(e,n)}function Al(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,bt(e,n)}}function $l(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===l?a=l=o:l=l.next=o,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Vl(e,t,n,r){var a=e.updateQueue;Rl=!1;var l=a.firstBaseUpdate,o=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var s=i,u=s.next;s.next=null,null===o?l=u:o.next=u,o=s;var c=e.alternate;null!==c&&(i=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===i?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s)}if(null!==l){var d=a.baseState;for(o=0,c=u=s=null,i=l;;){var f=i.lane,p=i.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var m=e,h=i;switch(f=t,p=n,h.tag){case 1:if("function"==typeof(m=h.payload)){d=m.call(p,d,f);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null==(f="function"==typeof(m=h.payload)?m.call(p,d,f):m))break e;d=O({},d,f);break e;case 2:Rl=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[i]:f.push(i))}else p={eventTime:p,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,o|=f;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{o|=a.lane,a=a.next}while(a!==t)}else null===l&&(a.shared.lanes=0);Is|=o,e.lanes=o,e.memoizedState=d}}function Bl(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!=typeof a)throw Error(l(191,a));a.call(r)}}}var Wl={},Hl=Na(Wl),Ql=Na(Wl),ql=Na(Wl);function Kl(e){if(e===Wl)throw Error(l(174));return e}function Gl(e,t){switch(Ca(ql,t),Ca(Ql,e),Ca(Hl,Wl),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ea(Hl),Ca(Hl,t)}function Yl(){Ea(Hl),Ea(Ql),Ea(ql)}function Xl(e){Kl(ql.current);var t=Kl(Hl.current),n=se(t,e.type);t!==n&&(Ca(Ql,e),Ca(Hl,n))}function Zl(e){Ql.current===e&&(Ea(Hl),Ea(Ql))}var Jl=Na(0);function eo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var to=[];function no(){for(var e=0;e<to.length;e++)to[e]._workInProgressVersionPrimary=null;to.length=0}var ro=x.ReactCurrentDispatcher,ao=x.ReactCurrentBatchConfig,lo=0,oo=null,io=null,so=null,uo=!1,co=!1,fo=0,po=0;function mo(){throw Error(l(321))}function ho(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ir(e[n],t[n]))return!1;return!0}function go(e,t,n,r,a,o){if(lo=o,oo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ro.current=null===e||null===e.memoizedState?Jo:ei,e=n(r,a),co){o=0;do{if(co=!1,fo=0,25<=o)throw Error(l(301));o+=1,so=io=null,t.updateQueue=null,ro.current=ti,e=n(r,a)}while(co)}if(ro.current=Zo,t=null!==io&&null!==io.next,lo=0,so=io=oo=null,uo=!1,t)throw Error(l(300));return e}function vo(){var e=0!==fo;return fo=0,e}function bo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===so?oo.memoizedState=so=e:so=so.next=e,so}function yo(){if(null===io){var e=oo.alternate;e=null!==e?e.memoizedState:null}else e=io.next;var t=null===so?oo.memoizedState:so.next;if(null!==t)so=t,io=e;else{if(null===e)throw Error(l(310));e={memoizedState:(io=e).memoizedState,baseState:io.baseState,baseQueue:io.baseQueue,queue:io.queue,next:null},null===so?oo.memoizedState=so=e:so=so.next=e}return so}function xo(e,t){return"function"==typeof t?t(e):t}function wo(e){var t=yo(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=io,a=r.baseQueue,o=n.pending;if(null!==o){if(null!==a){var i=a.next;a.next=o.next,o.next=i}r.baseQueue=a=o,n.pending=null}if(null!==a){o=a.next,r=r.baseState;var s=i=null,u=null,c=o;do{var d=c.lane;if((lo&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(s=u=f,i=r):u=u.next=f,oo.lanes|=d,Is|=d}c=c.next}while(null!==c&&c!==o);null===u?i=r:u.next=s,ir(r,t.memoizedState)||(yi=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{o=a.lane,oo.lanes|=o,Is|=o,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ko(e){var t=yo(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{o=e(o,i.action),i=i.next}while(i!==a);ir(o,t.memoizedState)||(yi=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function So(){}function No(e,t){var n=oo,r=yo(),a=t(),o=!ir(r.memoizedState,a);if(o&&(r.memoizedState=a,yi=!0),r=r.queue,Io(jo.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||null!==so&&1&so.memoizedState.tag){if(n.flags|=2048,To(9,Co.bind(null,n,r,a,t),void 0,null),null===zs)throw Error(l(349));30&lo||Eo(n,t,a)}return a}function Eo(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Co(e,t,n,r){t.value=n,t.getSnapshot=r,_o(t)&&zo(e)}function jo(e,t,n){return n(function(){_o(t)&&zo(e)})}function _o(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ir(e,n)}catch(e){return!0}}function zo(e){var t=Ml(e,1);null!==t&&nu(t,e,1,-1)}function Po(e){var t=bo();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xo,lastRenderedState:e},t.queue=e,e=e.dispatch=Ko.bind(null,oo,e),[t.memoizedState,e]}function To(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Lo(){return yo().memoizedState}function Do(e,t,n,r){var a=bo();oo.flags|=e,a.memoizedState=To(1|t,n,void 0,void 0===r?null:r)}function Mo(e,t,n,r){var a=yo();r=void 0===r?null:r;var l=void 0;if(null!==io){var o=io.memoizedState;if(l=o.destroy,null!==r&&ho(r,o.deps))return void(a.memoizedState=To(t,n,l,r))}oo.flags|=e,a.memoizedState=To(1|t,n,l,r)}function Ro(e,t){return Do(8390656,8,e,t)}function Io(e,t){return Mo(2048,8,e,t)}function Oo(e,t){return Mo(4,2,e,t)}function Fo(e,t){return Mo(4,4,e,t)}function Uo(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ao(e,t,n){return n=null!=n?n.concat([e]):null,Mo(4,4,Uo.bind(null,t,e),n)}function $o(){}function Vo(e,t){var n=yo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ho(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Bo(e,t){var n=yo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ho(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Wo(e,t,n){return 21&lo?(ir(n,t)||(n=ht(),oo.lanes|=n,Is|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,yi=!0),e.memoizedState=n)}function Ho(e,t){var n=yt;yt=0!==n&&4>n?n:4,e(!0);var r=ao.transition;ao.transition={};try{e(!1),t()}finally{yt=n,ao.transition=r}}function Qo(){return yo().memoizedState}function qo(e,t,n){var r=tu(e);n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Go(e)?Yo(t,n):null!==(n=Dl(e,t,n,r))&&(nu(n,e,r,eu()),Xo(n,t,r))}function Ko(e,t,n){var r=tu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Go(e))Yo(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var o=t.lastRenderedState,i=l(o,n);if(a.hasEagerState=!0,a.eagerState=i,ir(i,o)){var s=t.interleaved;return null===s?(a.next=a,Ll(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(e){}null!==(n=Dl(e,t,a,r))&&(nu(n,e,r,a=eu()),Xo(n,t,r))}}function Go(e){var t=e.alternate;return e===oo||null!==t&&t===oo}function Yo(e,t){co=uo=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Xo(e,t,n){if(4194240&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,bt(e,n)}}var Zo={readContext:Pl,useCallback:mo,useContext:mo,useEffect:mo,useImperativeHandle:mo,useInsertionEffect:mo,useLayoutEffect:mo,useMemo:mo,useReducer:mo,useRef:mo,useState:mo,useDebugValue:mo,useDeferredValue:mo,useTransition:mo,useMutableSource:mo,useSyncExternalStore:mo,useId:mo,unstable_isNewReconciler:!1},Jo={readContext:Pl,useCallback:function(e,t){return bo().memoizedState=[e,void 0===t?null:t],e},useContext:Pl,useEffect:Ro,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,Do(4194308,4,Uo.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Do(4194308,4,e,t)},useInsertionEffect:function(e,t){return Do(4,2,e,t)},useMemo:function(e,t){var n=bo();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=bo();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=qo.bind(null,oo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},bo().memoizedState=e},useState:Po,useDebugValue:$o,useDeferredValue:function(e){return bo().memoizedState=e},useTransition:function(){var e=Po(!1),t=e[0];return e=Ho.bind(null,e[1]),bo().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=oo,a=bo();if(al){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===zs)throw Error(l(349));30&lo||Eo(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,Ro(jo.bind(null,r,o,e),[e]),r.flags|=2048,To(9,Co.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=bo(),t=zs.identifierPrefix;if(al){var n=Xa;t=":"+t+"R"+(n=(Ya&~(1<<32-ot(Ya)-1)).toString(32)+n),0<(n=fo++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=po++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ei={readContext:Pl,useCallback:Vo,useContext:Pl,useEffect:Io,useImperativeHandle:Ao,useInsertionEffect:Oo,useLayoutEffect:Fo,useMemo:Bo,useReducer:wo,useRef:Lo,useState:function(){return wo(xo)},useDebugValue:$o,useDeferredValue:function(e){return Wo(yo(),io.memoizedState,e)},useTransition:function(){return[wo(xo)[0],yo().memoizedState]},useMutableSource:So,useSyncExternalStore:No,useId:Qo,unstable_isNewReconciler:!1},ti={readContext:Pl,useCallback:Vo,useContext:Pl,useEffect:Io,useImperativeHandle:Ao,useInsertionEffect:Oo,useLayoutEffect:Fo,useMemo:Bo,useReducer:ko,useRef:Lo,useState:function(){return ko(xo)},useDebugValue:$o,useDeferredValue:function(e){var t=yo();return null===io?t.memoizedState=e:Wo(t,io.memoizedState,e)},useTransition:function(){return[ko(xo)[0],yo().memoizedState]},useMutableSource:So,useSyncExternalStore:No,useId:Qo,unstable_isNewReconciler:!1};function ni(e,t){if(e&&e.defaultProps){for(var n in t=O({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function ri(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:O({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ai={isMounted:function(e){return!!(e=e._reactInternals)&&Ve(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),l=Fl(r,a);l.payload=t,null!=n&&(l.callback=n),null!==(t=Ul(e,l,a))&&(nu(t,e,a,r),Al(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),l=Fl(r,a);l.tag=1,l.payload=t,null!=n&&(l.callback=n),null!==(t=Ul(e,l,a))&&(nu(t,e,a,r),Al(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),a=Fl(n,r);a.tag=2,null!=t&&(a.callback=t),null!==(t=Ul(e,a,r))&&(nu(t,e,r,n),Al(t,e,r))}};function li(e,t,n,r,a,l,o){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,o):!(t.prototype&&t.prototype.isPureReactComponent&&sr(n,r)&&sr(a,l))}function oi(e,t,n){var r=!1,a=ja,l=t.contextType;return"object"==typeof l&&null!==l?l=Pl(l):(a=La(t)?Pa:_a.current,l=(r=null!=(r=t.contextTypes))?Ta(e,a):ja),t=new t(n,l),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ai,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=l),t}function ii(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ai.enqueueReplaceState(t,t.state,null)}function si(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Il(e);var l=t.contextType;"object"==typeof l&&null!==l?a.context=Pl(l):(l=La(t)?Pa:_a.current,a.context=Ta(e,l)),a.state=e.memoizedState,"function"==typeof(l=t.getDerivedStateFromProps)&&(ri(e,t,l,n),a.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||(t=a.state,"function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&ai.enqueueReplaceState(a,a.state,null),Vl(e,n,a,r),a.state=e.memoizedState),"function"==typeof a.componentDidMount&&(e.flags|=4194308)}function ui(e,t){try{var n="",r=t;do{n+=$(r),r=r.return}while(r);var a=n}catch(e){a="\nError generating stack: "+e.message+"\n"+e.stack}return{value:e,source:t,stack:a,digest:null}}function ci(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function di(e,t){try{console.error(t.value)}catch(e){setTimeout(function(){throw e})}}var fi="function"==typeof WeakMap?WeakMap:Map;function pi(e,t,n){(n=Fl(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ws||(Ws=!0,Hs=r),di(0,t)},n}function mi(e,t,n){(n=Fl(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){di(0,t)}}var l=e.stateNode;return null!==l&&"function"==typeof l.componentDidCatch&&(n.callback=function(){di(0,t),"function"!=typeof r&&(null===Qs?Qs=new Set([this]):Qs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function hi(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fi;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Eu.bind(null,e,t,n),t.then(e,e))}function gi(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function vi(e,t,n,r,a){return 1&e.mode?(e.flags|=65536,e.lanes=a,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Fl(-1,1)).tag=2,Ul(n,t,1))),n.lanes|=1),e)}var bi=x.ReactCurrentOwner,yi=!1;function xi(e,t,n,r){t.child=null===e?wl(t,null,n,r):xl(t,e.child,n,r)}function wi(e,t,n,r,a){n=n.render;var l=t.ref;return zl(t,a),r=go(e,t,n,r,l,a),n=vo(),null===e||yi?(al&&n&&el(t),t.flags|=1,xi(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Wi(e,t,a))}function ki(e,t,n,r,a){if(null===e){var l=n.type;return"function"!=typeof l||Lu(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Mu(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,Si(e,t,l,r,a))}if(l=e.child,0===(e.lanes&a)){var o=l.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(o,r)&&e.ref===t.ref)return Wi(e,t,a)}return t.flags|=1,(e=Du(l,r)).ref=t.ref,e.return=t,t.child=e}function Si(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(sr(l,r)&&e.ref===t.ref){if(yi=!1,t.pendingProps=r=l,0===(e.lanes&a))return t.lanes=e.lanes,Wi(e,t,a);131072&e.flags&&(yi=!0)}}return Ci(e,t,n,r,a)}function Ni(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(1&t.mode){if(!(1073741824&n))return e=null!==l?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ca(Ds,Ls),Ls|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==l?l.baseLanes:n,Ca(Ds,Ls),Ls|=r}else t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ca(Ds,Ls),Ls|=n;else null!==l?(r=l.baseLanes|n,t.memoizedState=null):r=n,Ca(Ds,Ls),Ls|=r;return xi(e,t,a,n),t.child}function Ei(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ci(e,t,n,r,a){var l=La(n)?Pa:_a.current;return l=Ta(t,l),zl(t,a),n=go(e,t,n,r,l,a),r=vo(),null===e||yi?(al&&r&&el(t),t.flags|=1,xi(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Wi(e,t,a))}function ji(e,t,n,r,a){if(La(n)){var l=!0;Ia(t)}else l=!1;if(zl(t,a),null===t.stateNode)Bi(e,t),oi(t,n,r),si(t,n,r,a),r=!0;else if(null===e){var o=t.stateNode,i=t.memoizedProps;o.props=i;var s=o.context,u=n.contextType;u="object"==typeof u&&null!==u?Pl(u):Ta(t,u=La(n)?Pa:_a.current);var c=n.getDerivedStateFromProps,d="function"==typeof c||"function"==typeof o.getSnapshotBeforeUpdate;d||"function"!=typeof o.UNSAFE_componentWillReceiveProps&&"function"!=typeof o.componentWillReceiveProps||(i!==r||s!==u)&&ii(t,o,r,u),Rl=!1;var f=t.memoizedState;o.state=f,Vl(t,r,o,a),s=t.memoizedState,i!==r||f!==s||za.current||Rl?("function"==typeof c&&(ri(t,n,c,r),s=t.memoizedState),(i=Rl||li(t,n,i,r,f,s,u))?(d||"function"!=typeof o.UNSAFE_componentWillMount&&"function"!=typeof o.componentWillMount||("function"==typeof o.componentWillMount&&o.componentWillMount(),"function"==typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"==typeof o.componentDidMount&&(t.flags|=4194308)):("function"==typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),o.props=r,o.state=s,o.context=u,r=i):("function"==typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Ol(e,t),i=t.memoizedProps,u=t.type===t.elementType?i:ni(t.type,i),o.props=u,d=t.pendingProps,f=o.context,s="object"==typeof(s=n.contextType)&&null!==s?Pl(s):Ta(t,s=La(n)?Pa:_a.current);var p=n.getDerivedStateFromProps;(c="function"==typeof p||"function"==typeof o.getSnapshotBeforeUpdate)||"function"!=typeof o.UNSAFE_componentWillReceiveProps&&"function"!=typeof o.componentWillReceiveProps||(i!==d||f!==s)&&ii(t,o,r,s),Rl=!1,f=t.memoizedState,o.state=f,Vl(t,r,o,a);var m=t.memoizedState;i!==d||f!==m||za.current||Rl?("function"==typeof p&&(ri(t,n,p,r),m=t.memoizedState),(u=Rl||li(t,n,u,r,f,m,s)||!1)?(c||"function"!=typeof o.UNSAFE_componentWillUpdate&&"function"!=typeof o.componentWillUpdate||("function"==typeof o.componentWillUpdate&&o.componentWillUpdate(r,m,s),"function"==typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,m,s)),"function"==typeof o.componentDidUpdate&&(t.flags|=4),"function"==typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),o.props=r,o.state=m,o.context=s,r=u):("function"!=typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return _i(e,t,n,r,l,a)}function _i(e,t,n,r,a,l){Ei(e,t);var o=!!(128&t.flags);if(!r&&!o)return a&&Oa(t,n,!1),Wi(e,t,l);r=t.stateNode,bi.current=t;var i=o&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&o?(t.child=xl(t,e.child,null,l),t.child=xl(t,null,i,l)):xi(e,t,i,l),t.memoizedState=r.state,a&&Oa(t,n,!0),t.child}function zi(e){var t=e.stateNode;t.pendingContext?Ma(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Ma(0,t.context,!1),Gl(e,t.containerInfo)}function Pi(e,t,n,r,a){return pl(),ml(a),t.flags|=256,xi(e,t,n,r),t.child}var Ti,Li,Di,Mi,Ri={dehydrated:null,treeContext:null,retryLane:0};function Ii(e){return{baseLanes:e,cachePool:null,transitions:null}}function Oi(e,t,n){var r,a=t.pendingProps,o=Jl.current,i=!1,s=!!(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&!!(2&o)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(o|=1),Ca(Jl,1&o),null===e)return ul(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(1&t.mode?"$!"===e.data?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=a.children,e=a.fallback,i?(a=t.mode,i=t.child,s={mode:"hidden",children:s},1&a||null===i?i=Iu(s,a,0,null):(i.childLanes=0,i.pendingProps=s),e=Ru(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ii(n),t.memoizedState=Ri,e):Fi(t,s));if(null!==(o=e.memoizedState)&&null!==(r=o.dehydrated))return function(e,t,n,r,a,o,i){if(n)return 256&t.flags?(t.flags&=-257,Ui(e,t,i,r=ci(Error(l(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(o=r.fallback,a=t.mode,r=Iu({mode:"visible",children:r.children},a,0,null),(o=Ru(o,a,i,null)).flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,1&t.mode&&xl(t,e.child,null,i),t.child.memoizedState=Ii(i),t.memoizedState=Ri,o);if(!(1&t.mode))return Ui(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Ui(e,t,i,r=ci(o=Error(l(419)),r,void 0))}if(s=0!==(i&e.childLanes),yi||s){if(null!==(r=zs)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==o.retryLane&&(o.retryLane=a,Ml(e,a),nu(r,e,a,-1))}return hu(),Ui(e,t,i,r=ci(Error(l(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=ju.bind(null,e),a._reactRetry=t,null):(e=o.treeContext,rl=ua(a.nextSibling),nl=t,al=!0,ll=null,null!==e&&(qa[Ka++]=Ya,qa[Ka++]=Xa,qa[Ka++]=Ga,Ya=e.id,Xa=e.overflow,Ga=t),(t=Fi(t,r.children)).flags|=4096,t)}(e,t,s,a,r,o,n);if(i){i=a.fallback,s=t.mode,r=(o=e.child).sibling;var u={mode:"hidden",children:a.children};return 1&s||t.child===o?(a=Du(o,u)).subtreeFlags=14680064&o.subtreeFlags:((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null),null!==r?i=Du(r,i):(i=Ru(i,s,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,s=null===(s=e.child.memoizedState)?Ii(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Ri,a}return e=(i=e.child).sibling,a=Du(i,{mode:"visible",children:a.children}),!(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Fi(e,t){return(t=Iu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Ui(e,t,n,r){return null!==r&&ml(r),xl(t,e.child,null,n),(e=Fi(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Ai(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),_l(e.return,t,n)}function $i(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Vi(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(xi(e,t,r.children,n),2&(r=Jl.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Ai(e,n,t);else if(19===e.tag)Ai(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ca(Jl,r),1&t.mode)switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===eo(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),$i(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===eo(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}$i(t,!0,n,null,l);break;case"together":$i(t,!1,null,null,void 0);break;default:t.memoizedState=null}else t.memoizedState=null;return t.child}function Bi(e,t){!(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Wi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Is|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=Du(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Du(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Hi(e,t){if(!al)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Qi(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function qi(e,t,n){var r=t.pendingProps;switch(tl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Qi(t),null;case 1:case 17:return La(t.type)&&Da(),Qi(t),null;case 3:return r=t.stateNode,Yl(),Ea(za),Ea(_a),no(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(dl(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,null!==ll&&(ou(ll),ll=null))),Li(e,t),Qi(t),null;case 5:Zl(t);var a=Kl(ql.current);if(n=t.type,null!==e&&null!=t.stateNode)Di(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(l(166));return Qi(t),null}if(e=Kl(Hl.current),dl(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[fa]=t,r[pa]=o,e=!!(1&t.mode),n){case"dialog":Ur("cancel",r),Ur("close",r);break;case"iframe":case"object":case"embed":Ur("load",r);break;case"video":case"audio":for(a=0;a<Rr.length;a++)Ur(Rr[a],r);break;case"source":Ur("error",r);break;case"img":case"image":case"link":Ur("error",r),Ur("load",r);break;case"details":Ur("toggle",r);break;case"input":Y(r,o),Ur("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Ur("invalid",r);break;case"textarea":ae(r,o),Ur("invalid",r)}for(var s in be(n,o),a=null,o)if(o.hasOwnProperty(s)){var u=o[s];"children"===s?"string"==typeof u?r.textContent!==u&&(!0!==o.suppressHydrationWarning&&Zr(r.textContent,u,e),a=["children",u]):"number"==typeof u&&r.textContent!==""+u&&(!0!==o.suppressHydrationWarning&&Zr(r.textContent,u,e),a=["children",""+u]):i.hasOwnProperty(s)&&null!=u&&"onScroll"===s&&Ur("scroll",r)}switch(n){case"input":Q(r),J(r,o,!0);break;case"textarea":Q(r),oe(r);break;case"select":case"option":break;default:"function"==typeof o.onClick&&(r.onclick=Jr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ie(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[fa]=t,e[pa]=r,Ti(e,t,!1,!1),t.stateNode=e;e:{switch(s=ye(n,r),n){case"dialog":Ur("cancel",e),Ur("close",e),a=r;break;case"iframe":case"object":case"embed":Ur("load",e),a=r;break;case"video":case"audio":for(a=0;a<Rr.length;a++)Ur(Rr[a],e);a=r;break;case"source":Ur("error",e),a=r;break;case"img":case"image":case"link":Ur("error",e),Ur("load",e),a=r;break;case"details":Ur("toggle",e),a=r;break;case"input":Y(e,r),a=G(e,r),Ur("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=O({},r,{value:void 0}),Ur("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Ur("invalid",e)}for(o in be(n,a),u=a)if(u.hasOwnProperty(o)){var c=u[o];"style"===o?ge(e,c):"dangerouslySetInnerHTML"===o?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===o?"string"==typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"==typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==o&&"suppressHydrationWarning"!==o&&"autoFocus"!==o&&(i.hasOwnProperty(o)?null!=c&&"onScroll"===o&&Ur("scroll",e):null!=c&&y(e,o,c,s))}switch(n){case"input":Q(e),J(e,r,!1);break;case"textarea":Q(e),oe(e);break;case"option":null!=r.value&&e.setAttribute("value",""+W(r.value));break;case"select":e.multiple=!!r.multiple,null!=(o=r.value)?ne(e,!!r.multiple,o,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"==typeof a.onClick&&(e.onclick=Jr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Qi(t),null;case 6:if(e&&null!=t.stateNode)Mi(e,t,e.memoizedProps,r);else{if("string"!=typeof r&&null===t.stateNode)throw Error(l(166));if(n=Kl(ql.current),Kl(Hl.current),dl(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(o=r.nodeValue!==n)&&null!==(e=nl))switch(e.tag){case 3:Zr(r.nodeValue,n,!!(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Zr(r.nodeValue,n,!!(1&e.mode))}o&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return Qi(t),null;case 13:if(Ea(Jl),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(al&&null!==rl&&1&t.mode&&!(128&t.flags))fl(),pl(),t.flags|=98560,o=!1;else if(o=dl(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(l(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(l(317));o[fa]=t}else pl(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Qi(t),o=!1}else null!==ll&&(ou(ll),ll=null),o=!0;if(!o)return 65536&t.flags?t:null}return 128&t.flags?(t.lanes=n,t):((r=null!==r)!=(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,1&t.mode&&(null===e||1&Jl.current?0===Ms&&(Ms=3):hu())),null!==t.updateQueue&&(t.flags|=4),Qi(t),null);case 4:return Yl(),Li(e,t),null===e&&Vr(t.stateNode.containerInfo),Qi(t),null;case 10:return jl(t.type._context),Qi(t),null;case 19:if(Ea(Jl),null===(o=t.memoizedState))return Qi(t),null;if(r=!!(128&t.flags),null===(s=o.rendering))if(r)Hi(o,!1);else{if(0!==Ms||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(s=eo(e))){for(t.flags|=128,Hi(o,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(o=n).flags&=14680066,null===(s=o.alternate)?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ca(Jl,1&Jl.current|2),t.child}e=e.sibling}null!==o.tail&&Xe()>Vs&&(t.flags|=128,r=!0,Hi(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=eo(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Hi(o,!0),null===o.tail&&"hidden"===o.tailMode&&!s.alternate&&!al)return Qi(t),null}else 2*Xe()-o.renderingStartTime>Vs&&1073741824!==n&&(t.flags|=128,r=!0,Hi(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=o.last)?n.sibling=s:t.child=s,o.last=s)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Xe(),t.sibling=null,n=Jl.current,Ca(Jl,r?1&n|2:1&n),t):(Qi(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&1&t.mode?!!(1073741824&Ls)&&(Qi(t),6&t.subtreeFlags&&(t.flags|=8192)):Qi(t),null;case 24:case 25:return null}throw Error(l(156,t.tag))}function Ki(e,t){switch(tl(t),t.tag){case 1:return La(t.type)&&Da(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Yl(),Ea(za),Ea(_a),no(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Zl(t),null;case 13:if(Ea(Jl),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));pl()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ea(Jl),null;case 4:return Yl(),null;case 10:return jl(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Ti=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Li=function(){},Di=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Kl(Hl.current);var l,o=null;switch(n){case"input":a=G(e,a),r=G(e,r),o=[];break;case"select":a=O({},a,{value:void 0}),r=O({},r,{value:void 0}),o=[];break;case"textarea":a=re(e,a),r=re(e,r),o=[];break;default:"function"!=typeof a.onClick&&"function"==typeof r.onClick&&(e.onclick=Jr)}for(c in be(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var s=a[c];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(i.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(s=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(null!=u||null!=s))if("style"===c)if(s){for(l in s)!s.hasOwnProperty(l)||u&&u.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in u)u.hasOwnProperty(l)&&s[l]!==u[l]&&(n||(n={}),n[l]=u[l])}else n||(o||(o=[]),o.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,s=s?s.__html:void 0,null!=u&&s!==u&&(o=o||[]).push(c,u)):"children"===c?"string"!=typeof u&&"number"!=typeof u||(o=o||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(i.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Ur("scroll",e),o||s===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}},Mi=function(e,t,n,r){n!==r&&(t.flags|=4)};var Gi=!1,Yi=!1,Xi="function"==typeof WeakSet?WeakSet:Set,Zi=null;function Ji(e,t){var n=e.ref;if(null!==n)if("function"==typeof n)try{n(null)}catch(n){Nu(e,t,n)}else n.current=null}function es(e,t,n){try{n()}catch(n){Nu(e,t,n)}}var ts=!1;function ns(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var l=a.destroy;a.destroy=void 0,void 0!==l&&es(t,n,l)}a=a.next}while(a!==r)}}function rs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function as(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"==typeof t?t(e):t.current=e}}function ls(e){var t=e.alternate;null!==t&&(e.alternate=null,ls(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ha],delete t[ga],delete t[va]),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function os(e){return 5===e.tag||3===e.tag||4===e.tag}function is(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||os(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ss(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Jr));else if(4!==r&&null!==(e=e.child))for(ss(e,t,n),e=e.sibling;null!==e;)ss(e,t,n),e=e.sibling}function us(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(us(e,t,n),e=e.sibling;null!==e;)us(e,t,n),e=e.sibling}var cs=null,ds=!1;function fs(e,t,n){for(n=n.child;null!==n;)ps(e,t,n),n=n.sibling}function ps(e,t,n){if(lt&&"function"==typeof lt.onCommitFiberUnmount)try{lt.onCommitFiberUnmount(at,n)}catch(e){}switch(n.tag){case 5:Yi||Ji(n,t);case 6:var r=cs,a=ds;cs=null,fs(e,t,n),ds=a,null!==(cs=r)&&(ds?(e=cs,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cs.removeChild(n.stateNode));break;case 18:null!==cs&&(ds?(e=cs,n=n.stateNode,8===e.nodeType?sa(e.parentNode,n):1===e.nodeType&&sa(e,n),Vt(e)):sa(cs,n.stateNode));break;case 4:r=cs,a=ds,cs=n.stateNode.containerInfo,ds=!0,fs(e,t,n),cs=r,ds=a;break;case 0:case 11:case 14:case 15:if(!Yi&&null!==(r=n.updateQueue)&&null!==(r=r.lastEffect)){a=r=r.next;do{var l=a,o=l.destroy;l=l.tag,void 0!==o&&(2&l||4&l)&&es(n,t,o),a=a.next}while(a!==r)}fs(e,t,n);break;case 1:if(!Yi&&(Ji(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(e){Nu(n,t,e)}fs(e,t,n);break;case 21:fs(e,t,n);break;case 22:1&n.mode?(Yi=(r=Yi)||null!==n.memoizedState,fs(e,t,n),Yi=r):fs(e,t,n);break;default:fs(e,t,n)}}function ms(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Xi),t.forEach(function(t){var r=_u.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function hs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var o=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 5:cs=s.stateNode,ds=!1;break e;case 3:case 4:cs=s.stateNode.containerInfo,ds=!0;break e}s=s.return}if(null===cs)throw Error(l(160));ps(o,i,a),cs=null,ds=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(e){Nu(a,t,e)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gs(t,e),t=t.sibling}function gs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(hs(t,e),vs(e),4&r){try{ns(3,e,e.return),rs(3,e)}catch(t){Nu(e,e.return,t)}try{ns(5,e,e.return)}catch(t){Nu(e,e.return,t)}}break;case 1:hs(t,e),vs(e),512&r&&null!==n&&Ji(n,n.return);break;case 5:if(hs(t,e),vs(e),512&r&&null!==n&&Ji(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(t){Nu(e,e.return,t)}}if(4&r&&null!=(a=e.stateNode)){var o=e.memoizedProps,i=null!==n?n.memoizedProps:o,s=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===s&&"radio"===o.type&&null!=o.name&&X(a,o),ye(s,i);var c=ye(s,o);for(i=0;i<u.length;i+=2){var d=u[i],f=u[i+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):y(a,d,f,c)}switch(s){case"input":Z(a,o);break;case"textarea":le(a,o);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!o.multiple;var m=o.value;null!=m?ne(a,!!o.multiple,m,!1):p!==!!o.multiple&&(null!=o.defaultValue?ne(a,!!o.multiple,o.defaultValue,!0):ne(a,!!o.multiple,o.multiple?[]:"",!1))}a[pa]=o}catch(t){Nu(e,e.return,t)}}break;case 6:if(hs(t,e),vs(e),4&r){if(null===e.stateNode)throw Error(l(162));a=e.stateNode,o=e.memoizedProps;try{a.nodeValue=o}catch(t){Nu(e,e.return,t)}}break;case 3:if(hs(t,e),vs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Vt(t.containerInfo)}catch(t){Nu(e,e.return,t)}break;case 4:default:hs(t,e),vs(e);break;case 13:hs(t,e),vs(e),8192&(a=e.child).flags&&(o=null!==a.memoizedState,a.stateNode.isHidden=o,!o||null!==a.alternate&&null!==a.alternate.memoizedState||($s=Xe())),4&r&&ms(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Yi=(c=Yi)||d,hs(t,e),Yi=c):hs(t,e),vs(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&1&e.mode)for(Zi=e,d=e.child;null!==d;){for(f=Zi=d;null!==Zi;){switch(m=(p=Zi).child,p.tag){case 0:case 11:case 14:case 15:ns(4,p,p.return);break;case 1:Ji(p,p.return);var h=p.stateNode;if("function"==typeof h.componentWillUnmount){r=p,n=p.return;try{t=r,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(e){Nu(r,n,e)}}break;case 5:Ji(p,p.return);break;case 22:if(null!==p.memoizedState){ws(f);continue}}null!==m?(m.return=p,Zi=m):ws(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"==typeof(o=a.style).setProperty?o.setProperty("display","none","important"):o.display="none":(s=f.stateNode,i=null!=(u=f.memoizedProps.style)&&u.hasOwnProperty("display")?u.display:null,s.style.display=he("display",i))}catch(t){Nu(e,e.return,t)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(t){Nu(e,e.return,t)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:hs(t,e),vs(e),4&r&&ms(e);case 21:}}function vs(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(os(n)){var r=n;break e}n=n.return}throw Error(l(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),us(e,is(e),a);break;case 3:case 4:var o=r.stateNode.containerInfo;ss(e,is(e),o);break;default:throw Error(l(161))}}catch(t){Nu(e,e.return,t)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function bs(e,t,n){Zi=e,ys(e,t,n)}function ys(e,t,n){for(var r=!!(1&e.mode);null!==Zi;){var a=Zi,l=a.child;if(22===a.tag&&r){var o=null!==a.memoizedState||Gi;if(!o){var i=a.alternate,s=null!==i&&null!==i.memoizedState||Yi;i=Gi;var u=Yi;if(Gi=o,(Yi=s)&&!u)for(Zi=a;null!==Zi;)s=(o=Zi).child,22===o.tag&&null!==o.memoizedState?ks(a):null!==s?(s.return=o,Zi=s):ks(a);for(;null!==l;)Zi=l,ys(l,t,n),l=l.sibling;Zi=a,Gi=i,Yi=u}xs(e)}else 8772&a.subtreeFlags&&null!==l?(l.return=a,Zi=l):xs(e)}}function xs(e){for(;null!==Zi;){var t=Zi;if(8772&t.flags){var n=t.alternate;try{if(8772&t.flags)switch(t.tag){case 0:case 11:case 15:Yi||rs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Yi)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ni(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;null!==o&&Bl(t,o,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Bl(t,i,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Vt(f)}}}break;default:throw Error(l(163))}Yi||512&t.flags&&as(t)}catch(e){Nu(t,t.return,e)}}if(t===e){Zi=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zi=n;break}Zi=t.return}}function ws(e){for(;null!==Zi;){var t=Zi;if(t===e){Zi=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zi=n;break}Zi=t.return}}function ks(e){for(;null!==Zi;){var t=Zi;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rs(4,t)}catch(e){Nu(t,n,e)}break;case 1:var r=t.stateNode;if("function"==typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(e){Nu(t,a,e)}}var l=t.return;try{as(t)}catch(e){Nu(t,l,e)}break;case 5:var o=t.return;try{as(t)}catch(e){Nu(t,o,e)}}}catch(e){Nu(t,t.return,e)}if(t===e){Zi=null;break}var i=t.sibling;if(null!==i){i.return=t.return,Zi=i;break}Zi=t.return}}var Ss,Ns=Math.ceil,Es=x.ReactCurrentDispatcher,Cs=x.ReactCurrentOwner,js=x.ReactCurrentBatchConfig,_s=0,zs=null,Ps=null,Ts=0,Ls=0,Ds=Na(0),Ms=0,Rs=null,Is=0,Os=0,Fs=0,Us=null,As=null,$s=0,Vs=1/0,Bs=null,Ws=!1,Hs=null,Qs=null,qs=!1,Ks=null,Gs=0,Ys=0,Xs=null,Zs=-1,Js=0;function eu(){return 6&_s?Xe():-1!==Zs?Zs:Zs=Xe()}function tu(e){return 1&e.mode?2&_s&&0!==Ts?Ts&-Ts:null!==hl.transition?(0===Js&&(Js=ht()),Js):0!==(e=yt)?e:e=void 0===(e=window.event)?16:Yt(e.type):1}function nu(e,t,n,r){if(50<Ys)throw Ys=0,Xs=null,Error(l(185));vt(e,n,r),2&_s&&e===zs||(e===zs&&(!(2&_s)&&(Os|=n),4===Ms&&iu(e,Ts)),ru(e,r),1===n&&0===_s&&!(1&t.mode)&&(Vs=Xe()+500,Ua&&Va()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=e.pendingLanes;0<l;){var o=31-ot(l),i=1<<o,s=a[o];-1===s?0!==(i&n)&&0===(i&r)||(a[o]=pt(i,t)):s<=t&&(e.expiredLanes|=i),l&=~i}}(e,t);var r=ft(e,e===zs?Ts:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){Ua=!0,$a(e)}(su.bind(null,e)):$a(su.bind(null,e)),oa(function(){!(6&_s)&&Va()}),n=null;else{switch(xt(r)){case 1:n=Je;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=zu(n,au.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function au(e,t){if(Zs=-1,Js=0,6&_s)throw Error(l(327));var n=e.callbackNode;if(ku()&&e.callbackNode!==n)return null;var r=ft(e,e===zs?Ts:0);if(0===r)return null;if(30&r||0!==(r&e.expiredLanes)||t)t=gu(e,r);else{t=r;var a=_s;_s|=2;var o=mu();for(zs===e&&Ts===t||(Bs=null,Vs=Xe()+500,fu(e,t));;)try{bu();break}catch(t){pu(e,t)}Cl(),Es.current=o,_s=a,null!==Ps?t=0:(zs=null,Ts=0,t=Ms)}if(0!==t){if(2===t&&0!==(a=mt(e))&&(r=a,t=lu(e,a)),1===t)throw n=Rs,fu(e,0),iu(e,r),ru(e,Xe()),n;if(6===t)iu(e,r);else{if(a=e.current.alternate,!(30&r||function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!ir(l(),a))return!1}catch(e){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)||(t=gu(e,r),2===t&&(o=mt(e),0!==o&&(r=o,t=lu(e,o))),1!==t)))throw n=Rs,fu(e,0),iu(e,r),ru(e,Xe()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(l(345));case 2:case 5:wu(e,As,Bs);break;case 3:if(iu(e,r),(130023424&r)===r&&10<(t=$s+500-Xe())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(wu.bind(null,e,As,Bs),t);break}wu(e,As,Bs);break;case 4:if(iu(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-ot(r);o=1<<i,(i=t[i])>a&&(a=i),r&=~o}if(r=a,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ns(r/1960))-r)){e.timeoutHandle=ra(wu.bind(null,e,As,Bs),r);break}wu(e,As,Bs);break;default:throw Error(l(329))}}}return ru(e,Xe()),e.callbackNode===n?au.bind(null,e):null}function lu(e,t){var n=Us;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=gu(e,t))&&(t=As,As=n,null!==t&&ou(t)),e}function ou(e){null===As?As=e:As.push.apply(As,e)}function iu(e,t){for(t&=~Fs,t&=~Os,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function su(e){if(6&_s)throw Error(l(327));ku();var t=ft(e,0);if(!(1&t))return ru(e,Xe()),null;var n=gu(e,t);if(0!==e.tag&&2===n){var r=mt(e);0!==r&&(t=r,n=lu(e,r))}if(1===n)throw n=Rs,fu(e,0),iu(e,t),ru(e,Xe()),n;if(6===n)throw Error(l(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wu(e,As,Bs),ru(e,Xe()),null}function uu(e,t){var n=_s;_s|=1;try{return e(t)}finally{0===(_s=n)&&(Vs=Xe()+500,Ua&&Va())}}function cu(e){null!==Ks&&0===Ks.tag&&!(6&_s)&&ku();var t=_s;_s|=1;var n=js.transition,r=yt;try{if(js.transition=null,yt=1,e)return e()}finally{yt=r,js.transition=n,!(6&(_s=t))&&Va()}}function du(){Ls=Ds.current,Ea(Ds)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Ps)for(n=Ps.return;null!==n;){var r=n;switch(tl(r),r.tag){case 1:null!=(r=r.type.childContextTypes)&&Da();break;case 3:Yl(),Ea(za),Ea(_a),no();break;case 5:Zl(r);break;case 4:Yl();break;case 13:case 19:Ea(Jl);break;case 10:jl(r.type._context);break;case 22:case 23:du()}n=n.return}if(zs=e,Ps=e=Du(e.current,null),Ts=Ls=t,Ms=0,Rs=null,Fs=Os=Is=0,As=Us=null,null!==Tl){for(t=0;t<Tl.length;t++)if(null!==(r=(n=Tl[t]).interleaved)){n.interleaved=null;var a=r.next,l=n.pending;if(null!==l){var o=l.next;l.next=a,r.next=o}n.pending=r}Tl=null}return e}function pu(e,t){for(;;){var n=Ps;try{if(Cl(),ro.current=Zo,uo){for(var r=oo.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}uo=!1}if(lo=0,so=io=oo=null,co=!1,fo=0,Cs.current=null,null===n||null===n.return){Ms=1,Rs=t,Ps=null;break}e:{var o=e,i=n.return,s=n,u=t;if(t=Ts,s.flags|=32768,null!==u&&"object"==typeof u&&"function"==typeof u.then){var c=u,d=s,f=d.tag;if(!(1&d.mode||0!==f&&11!==f&&15!==f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=gi(i);if(null!==m){m.flags&=-257,vi(m,i,s,0,t),1&m.mode&&hi(o,c,t),u=c;var h=(t=m).updateQueue;if(null===h){var g=new Set;g.add(u),t.updateQueue=g}else h.add(u);break e}if(!(1&t)){hi(o,c,t),hu();break e}u=Error(l(426))}else if(al&&1&s.mode){var v=gi(i);if(null!==v){!(65536&v.flags)&&(v.flags|=256),vi(v,i,s,0,t),ml(ui(u,s));break e}}o=u=ui(u,s),4!==Ms&&(Ms=2),null===Us?Us=[o]:Us.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t,$l(o,pi(0,u,t));break e;case 1:s=u;var b=o.type,y=o.stateNode;if(!(128&o.flags||"function"!=typeof b.getDerivedStateFromError&&(null===y||"function"!=typeof y.componentDidCatch||null!==Qs&&Qs.has(y)))){o.flags|=65536,t&=-t,o.lanes|=t,$l(o,mi(o,s,t));break e}}o=o.return}while(null!==o)}xu(n)}catch(e){t=e,Ps===n&&null!==n&&(Ps=n=n.return);continue}break}}function mu(){var e=Es.current;return Es.current=Zo,null===e?Zo:e}function hu(){0!==Ms&&3!==Ms&&2!==Ms||(Ms=4),null===zs||!(268435455&Is)&&!(268435455&Os)||iu(zs,Ts)}function gu(e,t){var n=_s;_s|=2;var r=mu();for(zs===e&&Ts===t||(Bs=null,fu(e,t));;)try{vu();break}catch(t){pu(e,t)}if(Cl(),_s=n,Es.current=r,null!==Ps)throw Error(l(261));return zs=null,Ts=0,Ms}function vu(){for(;null!==Ps;)yu(Ps)}function bu(){for(;null!==Ps&&!Ge();)yu(Ps)}function yu(e){var t=Ss(e.alternate,e,Ls);e.memoizedProps=e.pendingProps,null===t?xu(e):Ps=t,Cs.current=null}function xu(e){var t=e;do{var n=t.alternate;if(e=t.return,32768&t.flags){if(null!==(n=Ki(n,t)))return n.flags&=32767,void(Ps=n);if(null===e)return Ms=6,void(Ps=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}else if(null!==(n=qi(n,t,Ls)))return void(Ps=n);if(null!==(t=t.sibling))return void(Ps=t);Ps=t=e}while(null!==t);0===Ms&&(Ms=5)}function wu(e,t,n){var r=yt,a=js.transition;try{js.transition=null,yt=1,function(e,t,n,r){do{ku()}while(null!==Ks);if(6&_s)throw Error(l(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-ot(n),l=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~l}}(e,o),e===zs&&(Ps=zs=null,Ts=0),!(2064&n.subtreeFlags)&&!(2064&n.flags)||qs||(qs=!0,zu(tt,function(){return ku(),null})),o=!!(15990&n.flags),15990&n.subtreeFlags||o){o=js.transition,js.transition=null;var i=yt;yt=1;var s=_s;_s|=4,Cs.current=null,function(e,t){if(ea=Wt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(e){n=null;break e}var i=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var m;f!==n||0!==a&&3!==f.nodeType||(s=i+a),f!==o||0!==r&&3!==f.nodeType||(u=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(m=f.firstChild);)p=f,f=m;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=i),p===o&&++d===r&&(u=i),null!==(m=f.nextSibling))break;p=(f=p).parentNode}f=m}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Wt=!1,Zi=t;null!==Zi;)if(e=(t=Zi).child,1028&t.subtreeFlags&&null!==e)e.return=t,Zi=e;else for(;null!==Zi;){t=Zi;try{var h=t.alternate;if(1024&t.flags)switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==h){var g=h.memoizedProps,v=h.memoizedState,b=t.stateNode,y=b.getSnapshotBeforeUpdate(t.elementType===t.type?g:ni(t.type,g),v);b.__reactInternalSnapshotBeforeUpdate=y}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(l(163))}}catch(e){Nu(t,t.return,e)}if(null!==(e=t.sibling)){e.return=t.return,Zi=e;break}Zi=t.return}h=ts,ts=!1}(e,n),gs(n,e),mr(ta),Wt=!!ea,ta=ea=null,e.current=n,bs(n,e,a),Ye(),_s=s,yt=i,js.transition=o}else e.current=n;if(qs&&(qs=!1,Ks=e,Gs=a),0===(o=e.pendingLanes)&&(Qs=null),function(e){if(lt&&"function"==typeof lt.onCommitFiberRoot)try{lt.onCommitFiberRoot(at,e,void 0,!(128&~e.current.flags))}catch(e){}}(n.stateNode),ru(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)r((a=t[n]).value,{componentStack:a.stack,digest:a.digest});if(Ws)throw Ws=!1,e=Hs,Hs=null,e;!!(1&Gs)&&0!==e.tag&&ku(),1&(o=e.pendingLanes)?e===Xs?Ys++:(Ys=0,Xs=e):Ys=0,Va()}(e,t,n,r)}finally{js.transition=a,yt=r}return null}function ku(){if(null!==Ks){var e=xt(Gs),t=js.transition,n=yt;try{if(js.transition=null,yt=16>e?16:e,null===Ks)var r=!1;else{if(e=Ks,Ks=null,Gs=0,6&_s)throw Error(l(331));var a=_s;for(_s|=4,Zi=e.current;null!==Zi;){var o=Zi,i=o.child;if(16&Zi.flags){var s=o.deletions;if(null!==s){for(var u=0;u<s.length;u++){var c=s[u];for(Zi=c;null!==Zi;){var d=Zi;switch(d.tag){case 0:case 11:case 15:ns(8,d,o)}var f=d.child;if(null!==f)f.return=d,Zi=f;else for(;null!==Zi;){var p=(d=Zi).sibling,m=d.return;if(ls(d),d===c){Zi=null;break}if(null!==p){p.return=m,Zi=p;break}Zi=m}}}var h=o.alternate;if(null!==h){var g=h.child;if(null!==g){h.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Zi=o}}if(2064&o.subtreeFlags&&null!==i)i.return=o,Zi=i;else e:for(;null!==Zi;){if(2048&(o=Zi).flags)switch(o.tag){case 0:case 11:case 15:ns(9,o,o.return)}var b=o.sibling;if(null!==b){b.return=o.return,Zi=b;break e}Zi=o.return}}var y=e.current;for(Zi=y;null!==Zi;){var x=(i=Zi).child;if(2064&i.subtreeFlags&&null!==x)x.return=i,Zi=x;else e:for(i=y;null!==Zi;){if(2048&(s=Zi).flags)try{switch(s.tag){case 0:case 11:case 15:rs(9,s)}}catch(e){Nu(s,s.return,e)}if(s===i){Zi=null;break e}var w=s.sibling;if(null!==w){w.return=s.return,Zi=w;break e}Zi=s.return}}if(_s=a,Va(),lt&&"function"==typeof lt.onPostCommitFiberRoot)try{lt.onPostCommitFiberRoot(at,e)}catch(e){}r=!0}return r}finally{yt=n,js.transition=t}}return!1}function Su(e,t,n){e=Ul(e,t=pi(0,t=ui(n,t),1),1),t=eu(),null!==e&&(vt(e,1,t),ru(e,t))}function Nu(e,t,n){if(3===e.tag)Su(e,e,n);else for(;null!==t;){if(3===t.tag){Su(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Qs||!Qs.has(r))){t=Ul(t,e=mi(t,e=ui(n,e),1),1),e=eu(),null!==t&&(vt(t,1,e),ru(t,e));break}}t=t.return}}function Eu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,zs===e&&(Ts&n)===n&&(4===Ms||3===Ms&&(130023424&Ts)===Ts&&500>Xe()-$s?fu(e,0):Fs|=n),ru(e,t)}function Cu(e,t){0===t&&(1&e.mode?(t=ct,!(130023424&(ct<<=1))&&(ct=4194304)):t=1);var n=eu();null!==(e=Ml(e,t))&&(vt(e,t,n),ru(e,n))}function ju(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Cu(e,n)}function _u(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(l(314))}null!==r&&r.delete(t),Cu(e,n)}function zu(e,t){return qe(e,t)}function Pu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Tu(e,t,n,r){return new Pu(e,t,n,r)}function Lu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Du(e,t){var n=e.alternate;return null===n?((n=Tu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Mu(e,t,n,r,a,o){var i=2;if(r=e,"function"==typeof e)Lu(e)&&(i=1);else if("string"==typeof e)i=5;else e:switch(e){case S:return Ru(n.children,a,o,t);case N:i=8,a|=8;break;case E:return(e=Tu(12,n,t,2|a)).elementType=E,e.lanes=o,e;case z:return(e=Tu(13,n,t,a)).elementType=z,e.lanes=o,e;case P:return(e=Tu(19,n,t,a)).elementType=P,e.lanes=o,e;case D:return Iu(n,a,o,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case C:i=10;break e;case j:i=9;break e;case _:i=11;break e;case T:i=14;break e;case L:i=16,r=null;break e}throw Error(l(130,null==e?e:typeof e,""))}return(t=Tu(i,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Ru(e,t,n,r){return(e=Tu(7,e,r,t)).lanes=n,e}function Iu(e,t,n,r){return(e=Tu(22,e,r,t)).elementType=D,e.lanes=n,e.stateNode={isHidden:!1},e}function Ou(e,t,n){return(e=Tu(6,e,null,t)).lanes=n,e}function Fu(e,t,n){return(t=Tu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Uu(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Au(e,t,n,r,a,l,o,i,s){return e=new Uu(e,t,n,i,s),1===t?(t=1,!0===l&&(t|=8)):t=0,l=Tu(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Il(l),e}function $u(e){if(!e)return ja;e:{if(Ve(e=e._reactInternals)!==e||1!==e.tag)throw Error(l(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(La(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(l(171))}if(1===e.tag){var n=e.type;if(La(n))return Ra(e,n,t)}return t}function Vu(e,t,n,r,a,l,o,i,s){return(e=Au(n,r,!0,e,0,l,0,i,s)).context=$u(null),n=e.current,(l=Fl(r=eu(),a=tu(n))).callback=null!=t?t:null,Ul(n,l,a),e.current.lanes=a,vt(e,a,r),ru(e,r),e}function Bu(e,t,n,r){var a=t.current,l=eu(),o=tu(a);return n=$u(n),null===t.context?t.context=n:t.pendingContext=n,(t=Fl(l,o)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Ul(a,t,o))&&(nu(e,a,o,l),Al(e,a,o)),o}function Wu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Hu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Qu(e,t){Hu(e,t),(e=e.alternate)&&Hu(e,t)}Ss=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||za.current)yi=!0;else{if(0===(e.lanes&n)&&!(128&t.flags))return yi=!1,function(e,t,n){switch(t.tag){case 3:zi(t),pl();break;case 5:Xl(t);break;case 1:La(t.type)&&Ia(t);break;case 4:Gl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Ca(kl,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ca(Jl,1&Jl.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Oi(e,t,n):(Ca(Jl,1&Jl.current),null!==(e=Wi(e,t,n))?e.sibling:null);Ca(Jl,1&Jl.current);break;case 19:if(r=0!==(n&t.childLanes),128&e.flags){if(r)return Vi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Ca(Jl,Jl.current),r)break;return null;case 22:case 23:return t.lanes=0,Ni(e,t,n)}return Wi(e,t,n)}(e,t,n);yi=!!(131072&e.flags)}else yi=!1,al&&1048576&t.flags&&Ja(t,Qa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Bi(e,t),e=t.pendingProps;var a=Ta(t,_a.current);zl(t,n),a=go(null,t,r,e,a,n);var o=vo();return t.flags|=1,"object"==typeof a&&null!==a&&"function"==typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,La(r)?(o=!0,Ia(t)):o=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Il(t),a.updater=ai,t.stateNode=a,a._reactInternals=t,si(t,r,e,n),t=_i(null,t,r,!0,o,n)):(t.tag=0,al&&o&&el(t),xi(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Bi(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"==typeof e)return Lu(e)?1:0;if(null!=e){if((e=e.$$typeof)===_)return 11;if(e===T)return 14}return 2}(r),e=ni(r,e),a){case 0:t=Ci(null,t,r,e,n);break e;case 1:t=ji(null,t,r,e,n);break e;case 11:t=wi(null,t,r,e,n);break e;case 14:t=ki(null,t,r,ni(r.type,e),n);break e}throw Error(l(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Ci(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 1:return r=t.type,a=t.pendingProps,ji(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 3:e:{if(zi(t),null===e)throw Error(l(387));r=t.pendingProps,a=(o=t.memoizedState).element,Ol(e,t),Vl(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Pi(e,t,r,n,a=ui(Error(l(423)),t));break e}if(r!==a){t=Pi(e,t,r,n,a=ui(Error(l(424)),t));break e}for(rl=ua(t.stateNode.containerInfo.firstChild),nl=t,al=!0,ll=null,n=wl(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pl(),r===a){t=Wi(e,t,n);break e}xi(e,t,r,n)}t=t.child}return t;case 5:return Xl(t),null===e&&ul(t),r=t.type,a=t.pendingProps,o=null!==e?e.memoizedProps:null,i=a.children,na(r,a)?i=null:null!==o&&na(r,o)&&(t.flags|=32),Ei(e,t),xi(e,t,i,n),t.child;case 6:return null===e&&ul(t),null;case 13:return Oi(e,t,n);case 4:return Gl(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xl(t,null,r,n):xi(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,wi(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 7:return xi(e,t,t.pendingProps,n),t.child;case 8:case 12:return xi(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,o=t.memoizedProps,i=a.value,Ca(kl,r._currentValue),r._currentValue=i,null!==o)if(ir(o.value,i)){if(o.children===a.children&&!za.current){t=Wi(e,t,n);break e}}else for(null!==(o=t.child)&&(o.return=t);null!==o;){var s=o.dependencies;if(null!==s){i=o.child;for(var u=s.firstContext;null!==u;){if(u.context===r){if(1===o.tag){(u=Fl(-1,n&-n)).tag=2;var c=o.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}o.lanes|=n,null!==(u=o.alternate)&&(u.lanes|=n),_l(o.return,n,t),s.lanes|=n;break}u=u.next}}else if(10===o.tag)i=o.type===t.type?null:o.child;else if(18===o.tag){if(null===(i=o.return))throw Error(l(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),_l(i,n,t),i=o.sibling}else i=o.child;if(null!==i)i.return=o;else for(i=o;null!==i;){if(i===t){i=null;break}if(null!==(o=i.sibling)){o.return=i.return,i=o;break}i=i.return}o=i}xi(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,zl(t,n),r=r(a=Pl(a)),t.flags|=1,xi(e,t,r,n),t.child;case 14:return a=ni(r=t.type,t.pendingProps),ki(e,t,r,a=ni(r.type,a),n);case 15:return Si(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ni(r,a),Bi(e,t),t.tag=1,La(r)?(e=!0,Ia(t)):e=!1,zl(t,n),oi(t,r,a),si(t,r,a,n),_i(null,t,r,!0,e,n);case 19:return Vi(e,t,n);case 22:return Ni(e,t,n)}throw Error(l(156,t.tag))};var qu="function"==typeof reportError?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}function Gu(e){this._internalRoot=e}function Yu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zu(){}function Ju(e,t,n,r,a){var l=n._reactRootContainer;if(l){var o=l;if("function"==typeof a){var i=a;a=function(){var e=Wu(o);i.call(e)}}Bu(t,o,e,a)}else o=function(e,t,n,r,a){if(a){if("function"==typeof r){var l=r;r=function(){var e=Wu(o);l.call(e)}}var o=Vu(t,r,e,0,null,!1,0,"",Zu);return e._reactRootContainer=o,e[ma]=o.current,Vr(8===e.nodeType?e.parentNode:e),cu(),o}for(;a=e.lastChild;)e.removeChild(a);if("function"==typeof r){var i=r;r=function(){var e=Wu(s);i.call(e)}}var s=Au(e,0,!1,null,0,!1,0,"",Zu);return e._reactRootContainer=s,e[ma]=s.current,Vr(8===e.nodeType?e.parentNode:e),cu(function(){Bu(t,s,n,r)}),s}(n,t,e,a,r);return Wu(o)}Gu.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));Bu(e,t,null,null)},Gu.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu(function(){Bu(null,e,null,null)}),t[ma]=null}},Gu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Nt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Dt.length&&0!==t&&t<Dt[n].priority;n++);Dt.splice(n,0,e),0===n&&Ot(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(bt(t,1|n),ru(t,Xe()),!(6&_s)&&(Vs=Xe()+500,Va()))}break;case 13:cu(function(){var t=Ml(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}}),Qu(e,1)}},kt=function(e){if(13===e.tag){var t=Ml(e,134217728);null!==t&&nu(t,e,134217728,eu()),Qu(e,134217728)}},St=function(e){if(13===e.tag){var t=tu(e),n=Ml(e,t);null!==n&&nu(n,e,t,eu()),Qu(e,t)}},Nt=function(){return yt},Et=function(e,t){var n=yt;try{return yt=e,t()}finally{yt=n}},ke=function(e,t,n){switch(t){case"input":if(Z(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=wa(r);if(!a)throw Error(l(90));q(r),Z(r,a)}}}break;case"textarea":le(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},_e=uu,ze=cu;var ec={usingClientEntryPoint:!1,Events:[ya,xa,wa,Ce,je,uu]},tc={findFiberByHostInstance:ba,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=He(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{at=rc.inject(nc),lt=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Yu(t))throw Error(l(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:k,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Yu(e))throw Error(l(299));var n=!1,r="",a=qu;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Au(e,1,!1,null,0,n,0,r,a),e[ma]=t.current,Vr(8===e.nodeType?e.parentNode:e),new Ku(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return null===(e=He(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Xu(t))throw Error(l(200));return Ju(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Yu(e))throw Error(l(405));var r=null!=n&&n.hydratedSources||null,a=!1,o="",i=qu;if(null!=n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=Vu(t,null,e,1,null!=n?n:null,a,0,o,i),e[ma]=t.current,Vr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Gu(t)},t.render=function(e,t,n){if(!Xu(t))throw Error(l(200));return Ju(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Xu(e))throw Error(l(40));return!!e._reactRootContainer&&(cu(function(){Ju(null,null,e,!1,function(){e._reactRootContainer=null,e[ma]=null})}),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xu(n))throw Error(l(200));if(null==e||void 0===e._reactInternals)throw Error(l(38));return Ju(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},848:(e,t,n)=>{e.exports=n(20)},961:(e,t,n)=>{!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=n(551)},982:(e,t,n)=>{e.exports=n(463)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}var r=n(540),a=n(338);const l=e=>{const t=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()))(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim(),i=e=>{for(const t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const u=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:a,className:l="",children:u,iconNode:c,...d},f)=>(0,r.createElement)("svg",{ref:f,...s,width:t,height:t,stroke:e,strokeWidth:a?24*Number(n)/Number(t):n,className:o("lucide",l),...!u&&!i(d)&&{"aria-hidden":"true"},...d},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(u)?u:[u]])),c=(e,t)=>{const n=(0,r.forwardRef)(({className:n,...a},i)=>{return(0,r.createElement)(u,{ref:i,iconNode:t,className:o(`lucide-${s=l(e),s.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${e}`,n),...a});var s});return n.displayName=l(e),n},d=c("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),f=c("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),p=c("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),m=c("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),h=c("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);function g(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=g(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function v(){for(var e,t,n=0,r="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=g(e))&&(r&&(r+=" "),r+=t);return r}const b=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,y=v,x=(e,t)=>n=>{var r;if(null==(null==t?void 0:t.variants))return y(e,null==n?void 0:n.class,null==n?void 0:n.className);const{variants:a,defaultVariants:l}=t,o=Object.keys(a).map(e=>{const t=null==n?void 0:n[e],r=null==l?void 0:l[e];if(null===t)return null;const o=b(t)||b(r);return a[e][o]}),i=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{}),s=null==t||null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...a}=t;return Object.entries(a).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...l,...i}[t]):{...l,...i}[t]===n})?[...e,n,r]:e},[]);return y(e,o,s,null==n?void 0:n.class,null==n?void 0:n.className)},w=e=>{const t=E(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{const n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),k(n,t)||N(e)},getConflictingClassGroupIds:(e,t)=>{const a=n[e]||[];return t&&r[e]?[...a,...r[e]]:a}}},k=(e,t)=>{if(0===e.length)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),a=r?k(e.slice(1),r):void 0;if(a)return a;if(0===t.validators.length)return;const l=e.join("-");return t.validators.find(({validator:e})=>e(l))?.classGroupId},S=/^\[(.+)\]$/,N=e=>{if(S.test(e)){const t=S.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},E=e=>{const{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(const e in n)C(n[e],r,e,t);return r},C=(e,t,n,r)=>{e.forEach(e=>{if("string"!=typeof e){if("function"==typeof e)return _(e)?void C(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(([e,a])=>{C(a,j(t,e),n,r)})}else(""===e?t:j(t,e)).classGroupId=n})},j=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},_=e=>e.isThemeGetter,z=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const a=(a,l)=>{n.set(a,l),t++,t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(a(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):a(e,t)}}},P=e=>{const{prefix:t,experimentalParseClassName:n}=e;let r=e=>{const t=[];let n,r=0,a=0,l=0;for(let o=0;o<e.length;o++){let i=e[o];if(0===r&&0===a){if(":"===i){t.push(e.slice(l,o)),l=o+1;continue}if("/"===i){n=o;continue}}"["===i?r++:"]"===i?r--:"("===i?a++:")"===i&&a--}const o=0===t.length?e:e.substring(l),i=T(o);return{modifiers:t,hasImportantModifier:i!==o,baseClassName:i,maybePostfixModifierPosition:n&&n>l?n-l:void 0}};if(t){const e=t+":",n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){const e=r;r=t=>n({className:t,parseClassName:e})}return r},T=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,L=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;const n=[];let r=[];return e.forEach(e=>{"["===e[0]||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)}),n.push(...r.sort()),n}},D=/\s+/;function M(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=R(e))&&(r&&(r+=" "),r+=t);return r}const R=e=>{if("string"==typeof e)return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=R(e[r]))&&(n&&(n+=" "),n+=t);return n};function I(e,...t){let n,r,a,l=function(i){const s=t.reduce((e,t)=>t(e),e());return n=(e=>({cache:z(e.cacheSize),parseClassName:P(e),sortModifiers:L(e),...w(e)}))(s),r=n.cache.get,a=n.cache.set,l=o,o(i)};function o(e){const t=r(e);if(t)return t;const l=((e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:a,sortModifiers:l}=t,o=[],i=e.trim().split(D);let s="";for(let e=i.length-1;e>=0;e-=1){const t=i[e],{isExternal:u,modifiers:c,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}=n(t);if(u){s=t+(s.length>0?" "+s:s);continue}let m=!!p,h=r(m?f.substring(0,p):f);if(!h){if(!m){s=t+(s.length>0?" "+s:s);continue}if(h=r(f),!h){s=t+(s.length>0?" "+s:s);continue}m=!1}const g=l(c).join(":"),v=d?g+"!":g,b=v+h;if(o.includes(b))continue;o.push(b);const y=a(h,m);for(let e=0;e<y.length;++e){const t=y[e];o.push(v+t)}s=t+(s.length>0?" "+s:s)}return s})(e,n);return a(e,l),l}return function(){return l(M.apply(null,arguments))}}const O=e=>{const t=t=>t[e]||[];return t.isThemeGetter=!0,t},F=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,U=/^\((?:(\w[\w-]*):)?(.+)\)$/i,A=/^\d+\/\d+$/,$=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,V=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,B=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,W=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,H=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Q=e=>A.test(e),q=e=>!!e&&!Number.isNaN(Number(e)),K=e=>!!e&&Number.isInteger(Number(e)),G=e=>e.endsWith("%")&&q(e.slice(0,-1)),Y=e=>$.test(e),X=()=>!0,Z=e=>V.test(e)&&!B.test(e),J=()=>!1,ee=e=>W.test(e),te=e=>H.test(e),ne=e=>!ae(e)&&!ce(e),re=e=>ve(e,we,J),ae=e=>F.test(e),le=e=>ve(e,ke,Z),oe=e=>ve(e,Se,q),ie=e=>ve(e,ye,J),se=e=>ve(e,xe,te),ue=e=>ve(e,Ee,ee),ce=e=>U.test(e),de=e=>be(e,ke),fe=e=>be(e,Ne),pe=e=>be(e,ye),me=e=>be(e,we),he=e=>be(e,xe),ge=e=>be(e,Ee,!0),ve=(e,t,n)=>{const r=F.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},be=(e,t,n=!1)=>{const r=U.exec(e);return!!r&&(r[1]?t(r[1]):n)},ye=e=>"position"===e||"percentage"===e,xe=e=>"image"===e||"url"===e,we=e=>"length"===e||"size"===e||"bg-size"===e,ke=e=>"length"===e,Se=e=>"number"===e,Ne=e=>"family-name"===e,Ee=e=>"shadow"===e,Ce=I((Symbol.toStringTag,()=>{const e=O("color"),t=O("font"),n=O("text"),r=O("font-weight"),a=O("tracking"),l=O("leading"),o=O("breakpoint"),i=O("container"),s=O("spacing"),u=O("radius"),c=O("shadow"),d=O("inset-shadow"),f=O("text-shadow"),p=O("drop-shadow"),m=O("blur"),h=O("perspective"),g=O("aspect"),v=O("ease"),b=O("animate"),y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom",ce,ae],x=()=>[ce,ae,s],w=()=>[Q,"full","auto",...x()],k=()=>[K,"none","subgrid",ce,ae],S=()=>["auto",{span:["full",K,ce,ae]},K,ce,ae],N=()=>[K,"auto",ce,ae],E=()=>["auto","min","max","fr",ce,ae],C=()=>["auto",...x()],j=()=>[Q,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...x()],_=()=>[e,ce,ae],z=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom",pe,ie,{position:[ce,ae]}],P=()=>["auto","cover","contain",me,re,{size:[ce,ae]}],T=()=>[G,de,le],L=()=>["","none","full",u,ce,ae],D=()=>["",q,de,le],M=()=>[q,G,pe,ie],R=()=>["","none",m,ce,ae],I=()=>["none",q,ce,ae],F=()=>["none",q,ce,ae],U=()=>[q,ce,ae],A=()=>[Q,"full",...x()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Y],breakpoint:[Y],color:[X],container:[Y],"drop-shadow":[Y],ease:["in","out","in-out"],font:[ne],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Y],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Y],shadow:[Y],spacing:["px",q],text:[Y],"text-shadow":[Y],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Q,ae,ce,g]}],container:["container"],columns:[{columns:[q,ae,ce,i]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:y()}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:w()}],"inset-x":[{"inset-x":w()}],"inset-y":[{"inset-y":w()}],start:[{start:w()}],end:[{end:w()}],top:[{top:w()}],right:[{right:w()}],bottom:[{bottom:w()}],left:[{left:w()}],visibility:["visible","invisible","collapse"],z:[{z:[K,"auto",ce,ae]}],basis:[{basis:[Q,"full","auto",i,...x()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[q,Q,"auto","initial","none",ae]}],grow:[{grow:["",q,ce,ae]}],shrink:[{shrink:["",q,ce,ae]}],order:[{order:[K,"first","last","none",ce,ae]}],"grid-cols":[{"grid-cols":k()}],"col-start-end":[{col:S()}],"col-start":[{"col-start":N()}],"col-end":[{"col-end":N()}],"grid-rows":[{"grid-rows":k()}],"row-start-end":[{row:S()}],"row-start":[{"row-start":N()}],"row-end":[{"row-end":N()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":E()}],"auto-rows":[{"auto-rows":E()}],gap:[{gap:x()}],"gap-x":[{"gap-x":x()}],"gap-y":[{"gap-y":x()}],"justify-content":[{justify:["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe","normal"]}],"justify-items":[{"justify-items":["start","end","center","stretch","center-safe","end-safe","normal"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch","center-safe","end-safe"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"]}],"align-items":[{items:["start","end","center","stretch","center-safe","end-safe",{baseline:["","last"]}]}],"align-self":[{self:["auto","start","end","center","stretch","center-safe","end-safe",{baseline:["","last"]}]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"]}],"place-items":[{"place-items":["start","end","center","stretch","center-safe","end-safe","baseline"]}],"place-self":[{"place-self":["auto","start","end","center","stretch","center-safe","end-safe"]}],p:[{p:x()}],px:[{px:x()}],py:[{py:x()}],ps:[{ps:x()}],pe:[{pe:x()}],pt:[{pt:x()}],pr:[{pr:x()}],pb:[{pb:x()}],pl:[{pl:x()}],m:[{m:C()}],mx:[{mx:C()}],my:[{my:C()}],ms:[{ms:C()}],me:[{me:C()}],mt:[{mt:C()}],mr:[{mr:C()}],mb:[{mb:C()}],ml:[{ml:C()}],"space-x":[{"space-x":x()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":x()}],"space-y-reverse":["space-y-reverse"],size:[{size:j()}],w:[{w:[i,"screen",...j()]}],"min-w":[{"min-w":[i,"screen","none",...j()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[o]},...j()]}],h:[{h:["screen","lh",...j()]}],"min-h":[{"min-h":["screen","lh","none",...j()]}],"max-h":[{"max-h":["screen","lh",...j()]}],"font-size":[{text:["base",n,de,le]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,ce,oe]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",G,ae]}],"font-family":[{font:[fe,ae,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,ce,ae]}],"line-clamp":[{"line-clamp":[q,"none",ce,oe]}],leading:[{leading:[l,...x()]}],"list-image":[{"list-image":["none",ce,ae]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ce,ae]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:_()}],"text-color":[{text:_()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","wavy"]}],"text-decoration-thickness":[{decoration:[q,"from-font","auto",ce,le]}],"text-decoration-color":[{decoration:_()}],"underline-offset":[{"underline-offset":[q,"auto",ce,ae]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:x()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ce,ae]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ce,ae]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:z()}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:P()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},K,ce,ae],radial:["",ce,ae],conic:[K,ce,ae]},he,se]}],"bg-color":[{bg:_()}],"gradient-from-pos":[{from:T()}],"gradient-via-pos":[{via:T()}],"gradient-to-pos":[{to:T()}],"gradient-from":[{from:_()}],"gradient-via":[{via:_()}],"gradient-to":[{to:_()}],rounded:[{rounded:L()}],"rounded-s":[{"rounded-s":L()}],"rounded-e":[{"rounded-e":L()}],"rounded-t":[{"rounded-t":L()}],"rounded-r":[{"rounded-r":L()}],"rounded-b":[{"rounded-b":L()}],"rounded-l":[{"rounded-l":L()}],"rounded-ss":[{"rounded-ss":L()}],"rounded-se":[{"rounded-se":L()}],"rounded-ee":[{"rounded-ee":L()}],"rounded-es":[{"rounded-es":L()}],"rounded-tl":[{"rounded-tl":L()}],"rounded-tr":[{"rounded-tr":L()}],"rounded-br":[{"rounded-br":L()}],"rounded-bl":[{"rounded-bl":L()}],"border-w":[{border:D()}],"border-w-x":[{"border-x":D()}],"border-w-y":[{"border-y":D()}],"border-w-s":[{"border-s":D()}],"border-w-e":[{"border-e":D()}],"border-w-t":[{"border-t":D()}],"border-w-r":[{"border-r":D()}],"border-w-b":[{"border-b":D()}],"border-w-l":[{"border-l":D()}],"divide-x":[{"divide-x":D()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":D()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:["solid","dashed","dotted","double","hidden","none"]}],"divide-style":[{divide:["solid","dashed","dotted","double","hidden","none"]}],"border-color":[{border:_()}],"border-color-x":[{"border-x":_()}],"border-color-y":[{"border-y":_()}],"border-color-s":[{"border-s":_()}],"border-color-e":[{"border-e":_()}],"border-color-t":[{"border-t":_()}],"border-color-r":[{"border-r":_()}],"border-color-b":[{"border-b":_()}],"border-color-l":[{"border-l":_()}],"divide-color":[{divide:_()}],"outline-style":[{outline:["solid","dashed","dotted","double","none","hidden"]}],"outline-offset":[{"outline-offset":[q,ce,ae]}],"outline-w":[{outline:["",q,de,le]}],"outline-color":[{outline:_()}],shadow:[{shadow:["","none",c,ge,ue]}],"shadow-color":[{shadow:_()}],"inset-shadow":[{"inset-shadow":["none",d,ge,ue]}],"inset-shadow-color":[{"inset-shadow":_()}],"ring-w":[{ring:D()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:_()}],"ring-offset-w":[{"ring-offset":[q,le]}],"ring-offset-color":[{"ring-offset":_()}],"inset-ring-w":[{"inset-ring":D()}],"inset-ring-color":[{"inset-ring":_()}],"text-shadow":[{"text-shadow":["none",f,ge,ue]}],"text-shadow-color":[{"text-shadow":_()}],opacity:[{opacity:[q,ce,ae]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[q]}],"mask-image-linear-from-pos":[{"mask-linear-from":M()}],"mask-image-linear-to-pos":[{"mask-linear-to":M()}],"mask-image-linear-from-color":[{"mask-linear-from":_()}],"mask-image-linear-to-color":[{"mask-linear-to":_()}],"mask-image-t-from-pos":[{"mask-t-from":M()}],"mask-image-t-to-pos":[{"mask-t-to":M()}],"mask-image-t-from-color":[{"mask-t-from":_()}],"mask-image-t-to-color":[{"mask-t-to":_()}],"mask-image-r-from-pos":[{"mask-r-from":M()}],"mask-image-r-to-pos":[{"mask-r-to":M()}],"mask-image-r-from-color":[{"mask-r-from":_()}],"mask-image-r-to-color":[{"mask-r-to":_()}],"mask-image-b-from-pos":[{"mask-b-from":M()}],"mask-image-b-to-pos":[{"mask-b-to":M()}],"mask-image-b-from-color":[{"mask-b-from":_()}],"mask-image-b-to-color":[{"mask-b-to":_()}],"mask-image-l-from-pos":[{"mask-l-from":M()}],"mask-image-l-to-pos":[{"mask-l-to":M()}],"mask-image-l-from-color":[{"mask-l-from":_()}],"mask-image-l-to-color":[{"mask-l-to":_()}],"mask-image-x-from-pos":[{"mask-x-from":M()}],"mask-image-x-to-pos":[{"mask-x-to":M()}],"mask-image-x-from-color":[{"mask-x-from":_()}],"mask-image-x-to-color":[{"mask-x-to":_()}],"mask-image-y-from-pos":[{"mask-y-from":M()}],"mask-image-y-to-pos":[{"mask-y-to":M()}],"mask-image-y-from-color":[{"mask-y-from":_()}],"mask-image-y-to-color":[{"mask-y-to":_()}],"mask-image-radial":[{"mask-radial":[ce,ae]}],"mask-image-radial-from-pos":[{"mask-radial-from":M()}],"mask-image-radial-to-pos":[{"mask-radial-to":M()}],"mask-image-radial-from-color":[{"mask-radial-from":_()}],"mask-image-radial-to-color":[{"mask-radial-to":_()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"]}],"mask-image-conic-pos":[{"mask-conic":[q]}],"mask-image-conic-from-pos":[{"mask-conic-from":M()}],"mask-image-conic-to-pos":[{"mask-conic-to":M()}],"mask-image-conic-from-color":[{"mask-conic-from":_()}],"mask-image-conic-to-color":[{"mask-conic-to":_()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:z()}],"mask-repeat":[{mask:["no-repeat",{repeat:["","x","y","space","round"]}]}],"mask-size":[{mask:P()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ce,ae]}],filter:[{filter:["","none",ce,ae]}],blur:[{blur:R()}],brightness:[{brightness:[q,ce,ae]}],contrast:[{contrast:[q,ce,ae]}],"drop-shadow":[{"drop-shadow":["","none",p,ge,ue]}],"drop-shadow-color":[{"drop-shadow":_()}],grayscale:[{grayscale:["",q,ce,ae]}],"hue-rotate":[{"hue-rotate":[q,ce,ae]}],invert:[{invert:["",q,ce,ae]}],saturate:[{saturate:[q,ce,ae]}],sepia:[{sepia:["",q,ce,ae]}],"backdrop-filter":[{"backdrop-filter":["","none",ce,ae]}],"backdrop-blur":[{"backdrop-blur":R()}],"backdrop-brightness":[{"backdrop-brightness":[q,ce,ae]}],"backdrop-contrast":[{"backdrop-contrast":[q,ce,ae]}],"backdrop-grayscale":[{"backdrop-grayscale":["",q,ce,ae]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[q,ce,ae]}],"backdrop-invert":[{"backdrop-invert":["",q,ce,ae]}],"backdrop-opacity":[{"backdrop-opacity":[q,ce,ae]}],"backdrop-saturate":[{"backdrop-saturate":[q,ce,ae]}],"backdrop-sepia":[{"backdrop-sepia":["",q,ce,ae]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":x()}],"border-spacing-x":[{"border-spacing-x":x()}],"border-spacing-y":[{"border-spacing-y":x()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ce,ae]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[q,"initial",ce,ae]}],ease:[{ease:["linear","initial",v,ce,ae]}],delay:[{delay:[q,ce,ae]}],animate:[{animate:["none",b,ce,ae]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,ce,ae]}],"perspective-origin":[{"perspective-origin":y()}],rotate:[{rotate:I()}],"rotate-x":[{"rotate-x":I()}],"rotate-y":[{"rotate-y":I()}],"rotate-z":[{"rotate-z":I()}],scale:[{scale:F()}],"scale-x":[{"scale-x":F()}],"scale-y":[{"scale-y":F()}],"scale-z":[{"scale-z":F()}],"scale-3d":["scale-3d"],skew:[{skew:U()}],"skew-x":[{"skew-x":U()}],"skew-y":[{"skew-y":U()}],transform:[{transform:[ce,ae,"","none","gpu","cpu"]}],"transform-origin":[{origin:y()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:A()}],"translate-x":[{"translate-x":A()}],"translate-y":[{"translate-y":A()}],"translate-z":[{"translate-z":A()}],"translate-none":["translate-none"],accent:[{accent:_()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:_()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ce,ae]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":x()}],"scroll-mx":[{"scroll-mx":x()}],"scroll-my":[{"scroll-my":x()}],"scroll-ms":[{"scroll-ms":x()}],"scroll-me":[{"scroll-me":x()}],"scroll-mt":[{"scroll-mt":x()}],"scroll-mr":[{"scroll-mr":x()}],"scroll-mb":[{"scroll-mb":x()}],"scroll-ml":[{"scroll-ml":x()}],"scroll-p":[{"scroll-p":x()}],"scroll-px":[{"scroll-px":x()}],"scroll-py":[{"scroll-py":x()}],"scroll-ps":[{"scroll-ps":x()}],"scroll-pe":[{"scroll-pe":x()}],"scroll-pt":[{"scroll-pt":x()}],"scroll-pr":[{"scroll-pr":x()}],"scroll-pb":[{"scroll-pb":x()}],"scroll-pl":[{"scroll-pl":x()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ce,ae]}],fill:[{fill:["none",..._()]}],"stroke-w":[{stroke:[q,de,le,oe]}],stroke:[{stroke:["none",..._()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}}));function je(...e){return Ce(v(e))}var _e=n(848);const ze=x("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Pe=r.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...a},l)=>(0,_e.jsx)("button",{className:je(ze({variant:t,size:n,className:e})),ref:l,...a}));Pe.displayName="Button";const Te=r.forwardRef(({className:e,type:t,...n},r)=>(0,_e.jsx)("input",{type:t,className:je("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n}));Te.displayName="Input";const Le=c("pen-line",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]),De=c("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),Me=c("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Re=x("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Ie({className:e,variant:t,...n}){return(0,_e.jsx)("div",{className:je(Re({variant:t}),e),...n})}const Oe=(0,r.forwardRef)(({prompt:e,isSelected:t,folderName:n,searchQuery:a,onClick:l,onDoubleClick:o,onEdit:i,onDelete:s,onDragStart:u,onDragEnd:c,onDragOver:d,onDrop:f,isDragging:p,isDragOver:m},h)=>{const[g,v]=(0,r.useState)(!1),b=(e,t)=>{if(!t||!e)return e;const n=new RegExp(`(${t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")})`,"gi");return e.split(n).map((e,t)=>n.test(e)?(0,_e.jsx)("mark",{className:"highlight",children:e},t):e)};return(0,_e.jsxs)("div",{ref:h,className:je("group relative prompt-card cursor-pointer transition-all duration-200","mx-3 mb-3 p-4 bg-white/80 hover:bg-white border border-slate-200/60 rounded-xl hover:shadow-md hover:border-blue-200",t&&"ring-2 ring-blue-400 ring-opacity-50 bg-blue-50/50 border-blue-300",p&&"opacity-50 rotate-1 shadow-xl z-50 scale-105",m&&"border-t-2 border-t-blue-400 bg-blue-50/80 shadow-lg"),onClick:()=>{l()},onDoubleClick:()=>{o()},draggable:!0,onDragStart:t=>{v(!0),t.dataTransfer.effectAllowed="move",t.dataTransfer.setData("text/plain",e.id);const n=t.currentTarget.cloneNode(!0);n.style.opacity="0.8",n.style.transform="rotate(5deg)",document.body.appendChild(n),t.dataTransfer.setDragImage(n,0,0),setTimeout(()=>{document.body.removeChild(n)},0),u&&u(e,t)},onDragEnd:t=>{v(!1),c&&c(e,t)},onDragOver:t=>{t.preventDefault(),t.dataTransfer.dropEffect="move",d&&d(e,t)},onDrop:t=>{t.preventDefault();const n=t.dataTransfer.getData("text/plain");f&&n!==e.id&&f(n,e,t)},children:[(0,_e.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,_e.jsx)("h3",{className:"text-base font-semibold text-slate-800 truncate leading-tight flex-1 mr-3",children:b(e.title,a)}),(0,_e.jsxs)("div",{className:"flex items-center gap-2",children:[n&&(0,_e.jsx)(Ie,{variant:"secondary",className:"text-xs bg-slate-100 text-slate-600 border-slate-200 rounded-full px-2 py-1",children:b(n,a)}),(0,_e.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-all duration-200",children:[(0,_e.jsx)(Pe,{variant:"ghost",size:"icon",className:"h-7 w-7 rounded-lg hover:bg-blue-50 hover:text-blue-600 hover:scale-110 transition-all duration-200",onClick:t=>{t.stopPropagation(),i&&i(e)},title:"编辑",children:(0,_e.jsx)(Le,{className:"h-3.5 w-3.5"})}),(0,_e.jsx)(Pe,{variant:"ghost",size:"icon",className:"h-7 w-7 rounded-lg hover:bg-red-50 hover:text-red-600 hover:scale-110 transition-all duration-200",onClick:t=>{t.stopPropagation(),s&&window.confirm("确定要删除这个 Prompt 吗？")&&s(e.id)},title:"删除",children:(0,_e.jsx)(De,{className:"h-3.5 w-3.5"})}),(0,_e.jsx)(Pe,{variant:"ghost",size:"icon",className:"h-7 w-7 rounded-lg hover:bg-green-50 hover:text-green-600 hover:scale-110 transition-all duration-200",onClick:e=>{e.stopPropagation()},title:"查看详情",children:(0,_e.jsx)(Me,{className:"h-3.5 w-3.5"})})]})]})]}),e.description&&(0,_e.jsx)("p",{className:"text-sm text-slate-600 mb-3 leading-relaxed overflow-hidden",style:{display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical"},children:b(e.description,a)}),(0,_e.jsxs)("div",{className:"flex items-center justify-between",children:[(0,_e.jsx)("div",{className:"text-xs text-slate-500",children:e.usageCount>0&&(0,_e.jsxs)("span",{className:"bg-slate-100 px-2 py-1 rounded-full font-medium",children:["使用 ",e.usageCount," 次"]})}),(0,_e.jsx)("div",{className:"text-xs text-slate-400",children:e.updatedAt&&new Date(e.updatedAt).toLocaleDateString()})]})]})});Oe.displayName="PromptItem";const Fe=Oe,Ue=function({prompts:e,selectedIndex:t,onSelect:n,onInsert:a,getFolderName:l,searchQuery:o,onEdit:i,onDelete:s,onPromptMove:u}){const c=(0,r.useRef)(null),d=(0,r.useRef)(null),[f,p]=(0,r.useState)(null),[m,h]=(0,r.useState)(null);(0,r.useEffect)(()=>{if(d.current&&c.current){const e=c.current.getBoundingClientRect(),t=d.current.getBoundingClientRect();t.top<e.top?d.current.scrollIntoView({behavior:"smooth",block:"start"}):t.bottom>e.bottom&&d.current.scrollIntoView({behavior:"smooth",block:"end"})}},[t]);const g=(e,t)=>{p(e)},v=(e,t)=>{p(null),h(null)},b=(e,t)=>{f&&f.id!==e.id&&h(e)},y=(t,n,r)=>{const a=e.find(e=>e.id===t);a&&u&&u(a,n),p(null),h(null)};return 0===e.length?(0,_e.jsx)("div",{className:"flex-1 flex items-center justify-center p-8",children:(0,_e.jsx)("div",{className:"text-center max-w-sm",children:o?(0,_e.jsxs)(_e.Fragment,{children:[(0,_e.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-6 mx-auto",children:(0,_e.jsx)("span",{className:"text-2xl",children:"🔍"})}),(0,_e.jsx)("div",{className:"text-xl font-bold text-slate-800 mb-3",children:"未找到匹配的 Prompt"}),(0,_e.jsx)("div",{className:"text-sm text-slate-500 leading-relaxed",children:"尝试使用不同的关键词搜索，或者检查拼写是否正确"})]}):(0,_e.jsxs)(_e.Fragment,{children:[(0,_e.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-6 mx-auto",children:(0,_e.jsx)("span",{className:"text-2xl",children:"📝"})}),(0,_e.jsx)("div",{className:"text-xl font-bold text-slate-800 mb-3",children:"暂无 Prompt"}),(0,_e.jsx)("div",{className:"text-sm text-slate-500 leading-relaxed",children:'点击上方的"添加 Prompt"按钮创建您的第一个 Prompt，开始提升工作效率'})]})})}):(0,_e.jsx)("div",{className:"flex-1 overflow-y-auto",ref:c,children:e.map((e,r)=>(0,_e.jsx)(Fe,{ref:r===t?d:null,prompt:e,isSelected:r===t,folderName:l(e.folderId),searchQuery:o,onClick:()=>(e=>{n(e)})(r),onDoubleClick:()=>(e=>{a(e)})(e),onEdit:i,onDelete:s,onDragStart:g,onDragEnd:v,onDragOver:b,onDrop:y,isDragging:f&&f.id===e.id,isDragOver:m&&m.id===e.id},e.id))})},Ae=r.forwardRef(({className:e,...t},n)=>(0,_e.jsx)("div",{ref:n,className:je("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));Ae.displayName="Card";const $e=r.forwardRef(({className:e,...t},n)=>(0,_e.jsx)("div",{ref:n,className:je("flex flex-col space-y-1.5 p-6",e),...t}));$e.displayName="CardHeader";const Ve=r.forwardRef(({className:e,...t},n)=>(0,_e.jsx)("h3",{ref:n,className:je("text-2xl font-semibold leading-none tracking-tight",e),...t}));Ve.displayName="CardTitle",r.forwardRef(({className:e,...t},n)=>(0,_e.jsx)("p",{ref:n,className:je("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";const Be=r.forwardRef(({className:e,...t},n)=>(0,_e.jsx)("div",{ref:n,className:je("p-6 pt-0",e),...t}));Be.displayName="CardContent",r.forwardRef(({className:e,...t},n)=>(0,_e.jsx)("div",{ref:n,className:je("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter";const We=c("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]),He=c("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),Qe=c("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),qe=c("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),Ke=function({onClose:e,onDataImported:t}){const[n,a]=(0,r.useState)(!1),[l,o]=(0,r.useState)(!1),[i,s]=(0,r.useState)(""),[u,c]=(0,r.useState)(""),[d,f]=(0,r.useState)([]),[m,g]=(0,r.useState)(!1),[v,b]=(0,r.useState)(null),[y,x]=(0,r.useState)("");(0,r.useEffect)(()=>{w()},[]);const w=async()=>{try{const e=await k({action:"get-folders"});f(e||[])}catch(e){console.error("Error loading folders:",e)}},k=e=>new Promise((t,n)=>{chrome.runtime.sendMessage(e,e=>{chrome.runtime.lastError?n(chrome.runtime.lastError):t(e)})}),S=()=>{g(!1),b(null),x("")},N=async()=>{if(!y.trim())return s("文件夹名称不能为空"),void c("error");try{const e={...v,name:y.trim()},t=await k({action:"save-folder",folder:e});t.success?(s(v?"文件夹更新成功！":"文件夹创建成功！"),c("success"),S(),await w()):(s(`保存失败：${t.error}`),c("error"))}catch(e){console.error("Error saving folder:",e),s(`保存失败：${e.message}`),c("error")}};return(0,_e.jsxs)(Ae,{className:"w-full max-w-2xl max-h-[90vh] overflow-hidden shadow-lg",children:[(0,_e.jsxs)($e,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[(0,_e.jsx)(Ve,{className:"text-lg",children:"设置"}),(0,_e.jsx)(Pe,{variant:"ghost",size:"icon",onClick:e,className:"h-6 w-6",children:(0,_e.jsx)(p,{className:"h-4 w-4"})})]}),(0,_e.jsxs)(Be,{className:"overflow-y-auto space-y-6",children:[(0,_e.jsxs)("div",{className:"space-y-4",children:[(0,_e.jsxs)("div",{className:"flex items-center justify-between",children:[(0,_e.jsxs)("div",{children:[(0,_e.jsx)("h3",{className:"text-base font-semibold text-foreground",children:"文件夹管理"}),(0,_e.jsx)("p",{className:"text-sm text-muted-foreground",children:"管理您的 Prompt 文件夹"})]}),(0,_e.jsxs)(Pe,{onClick:()=>{b(null),x(""),g(!0)},size:"sm",className:"gap-2",children:[(0,_e.jsx)(h,{className:"h-4 w-4"}),"添加文件夹"]})]}),(0,_e.jsxs)("div",{className:"border rounded-lg overflow-hidden",children:[d.map(e=>(0,_e.jsxs)("div",{className:"flex items-center justify-between p-3 border-b last:border-b-0 hover:bg-muted/50 transition-colors",children:[(0,_e.jsxs)("div",{className:"flex items-center gap-3",children:[(0,_e.jsx)(We,{className:"h-4 w-4 text-muted-foreground"}),(0,_e.jsxs)("div",{children:[(0,_e.jsx)("div",{className:"font-medium text-sm text-foreground",children:e.name}),(0,_e.jsxs)("div",{className:"text-xs text-muted-foreground",children:["创建于 ",new Date(e.createdAt).toLocaleDateString()]})]})]}),(0,_e.jsxs)("div",{className:"flex items-center gap-1",children:[(0,_e.jsx)(Pe,{variant:"ghost",size:"icon",className:"h-6 w-6",onClick:()=>(e=>{b(e),x(e.name),g(!0)})(e),title:"编辑文件夹",children:(0,_e.jsx)(He,{className:"h-3 w-3"})}),(0,_e.jsx)(Pe,{variant:"ghost",size:"icon",className:"h-6 w-6 text-destructive hover:text-destructive",onClick:()=>(async e=>{if(window.confirm(`确定要删除文件夹"${e.name}"吗？此操作不可撤销。`))try{const t=await k({action:"delete-folder",folderId:e.id});t.success?(s("文件夹删除成功！"),c("success"),await w()):(s(`删除失败：${t.error}`),c("error"))}catch(e){console.error("Error deleting folder:",e),s(`删除失败：${e.message}`),c("error")}})(e),title:"删除文件夹",children:(0,_e.jsx)(De,{className:"h-3 w-3"})})]})]},e.id)),0===d.length&&(0,_e.jsx)("div",{className:"p-6 text-center text-sm text-muted-foreground",children:"暂无文件夹，点击上方按钮添加第一个文件夹"})]})]}),(0,_e.jsxs)("div",{className:"space-y-4",children:[(0,_e.jsxs)("div",{children:[(0,_e.jsx)("h3",{className:"text-base font-semibold text-foreground mb-2",children:"数据管理"}),(0,_e.jsx)("p",{className:"text-sm text-muted-foreground",children:"导入导出您的 Prompt 数据"})]}),(0,_e.jsxs)("div",{className:"grid gap-4",children:[(0,_e.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,_e.jsxs)("div",{children:[(0,_e.jsx)("h4",{className:"font-medium text-sm text-foreground",children:"导出数据"}),(0,_e.jsx)("p",{className:"text-xs text-muted-foreground",children:"将所有 Prompt 和文件夹导出为 JSON 文件"})]}),(0,_e.jsxs)(Pe,{onClick:async()=>{try{o(!0),s("");const e=await k({action:"export-data"});if(e.success){const t=new Blob([JSON.stringify(e.data,null,2)],{type:"application/json"}),n=URL.createObjectURL(t),r=document.createElement("a");r.href=n,r.download=`prompt-manager-backup-${(new Date).toISOString().split("T")[0]}.json`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(n),s("数据导出成功！"),c("success")}else s(`导出失败：${e.error}`),c("error")}catch(e){console.error("Export error:",e),s(`导出失败：${e.message}`),c("error")}finally{o(!1)}},disabled:l,size:"sm",className:"gap-2",children:[(0,_e.jsx)(Qe,{className:"h-4 w-4"}),l?"导出中...":"导出"]})]}),(0,_e.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,_e.jsxs)("div",{children:[(0,_e.jsx)("h4",{className:"font-medium text-sm text-foreground",children:"导入数据"}),(0,_e.jsx)("p",{className:"text-xs text-muted-foreground",children:"从 JSON 文件导入 Prompt 和文件夹（会覆盖现有数据）"})]}),(0,_e.jsxs)("div",{children:[(0,_e.jsx)("input",{type:"file",accept:".json",onChange:e=>{const n=e.target.files[0];if(!n)return;const r=new FileReader;r.onload=async n=>{try{a(!0),s("");const e=JSON.parse(n.target.result),r=await k({action:"import-data",data:e});r.success?(s(r.message||"数据导入成功！"),c("success"),t&&t()):(s(`导入失败：${r.error}`),c("error"))}catch(e){console.error("Import error:",e),s(`导入失败：${e.message}`),c("error")}finally{a(!1),e.target.value=""}},r.readAsText(n)},disabled:n,className:"hidden",id:"import-file"}),(0,_e.jsx)(Pe,{asChild:!0,disabled:n,size:"sm",variant:"outline",className:"gap-2",children:(0,_e.jsxs)("label",{htmlFor:"import-file",className:"cursor-pointer",children:[(0,_e.jsx)(qe,{className:"h-4 w-4"}),n?"导入中...":"选择文件"]})})]})]})]})]}),i&&(0,_e.jsxs)("div",{className:"p-3 rounded-lg border flex items-center justify-between "+("success"===u?"bg-green-50 border-green-200 text-green-800":"bg-red-50 border-red-200 text-red-800"),children:[(0,_e.jsx)("span",{className:"text-sm",children:i}),(0,_e.jsx)(Pe,{variant:"ghost",size:"icon",onClick:()=>{s(""),c("")},className:"h-6 w-6",children:(0,_e.jsx)(p,{className:"h-3 w-3"})})]})]}),m&&(0,_e.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,_e.jsxs)(Ae,{className:"w-full max-w-md shadow-lg",children:[(0,_e.jsxs)($e,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[(0,_e.jsx)(Ve,{className:"text-base",children:v?"编辑文件夹":"添加文件夹"}),(0,_e.jsx)(Pe,{variant:"ghost",size:"icon",onClick:S,className:"h-6 w-6",children:(0,_e.jsx)(p,{className:"h-4 w-4"})})]}),(0,_e.jsxs)(Be,{className:"space-y-4",children:[(0,_e.jsxs)("div",{className:"space-y-2",children:[(0,_e.jsx)("label",{htmlFor:"folder-name",className:"text-sm font-medium text-foreground",children:"文件夹名称"}),(0,_e.jsx)(Te,{id:"folder-name",value:y,onChange:e=>x(e.target.value),placeholder:"请输入文件夹名称",autoFocus:!0,onKeyDown:e=>{"Enter"===e.key?N():"Escape"===e.key&&S()}})]}),(0,_e.jsxs)("div",{className:"flex justify-end gap-2 pt-4",children:[(0,_e.jsx)(Pe,{variant:"outline",onClick:S,children:"取消"}),(0,_e.jsx)(Pe,{onClick:N,children:v?"更新":"创建"})]})]})]})})]})},Ge=r.forwardRef(({className:e,...t},n)=>(0,_e.jsx)("textarea",{className:je("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));Ge.displayName="Textarea";const Ye=r.forwardRef(({className:e,children:t,...n},r)=>(0,_e.jsx)("select",{className:je("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n,children:t}));Ye.displayName="Select";const Xe=function({prompt:e,folders:t,onSave:n,onCancel:a,onDelete:l}){const[o,i]=(0,r.useState)({title:"",description:"",content:"",folderId:""}),[s,u]=(0,r.useState)({}),[c,d]=(0,r.useState)(!1),[f,m]=(0,r.useState)(!1),h=!!e;(0,r.useEffect)(()=>{i(e?{title:e.title||"",description:e.description||"",content:e.content||"",folderId:e.folderId||""}:{title:"",description:"",content:"",folderId:""}),u({})},[e]);const g=(e,t)=>{i(n=>({...n,[e]:t})),s[e]&&u(t=>({...t,[e]:""}))},v=async()=>{if((()=>{const e={};return o.title.trim()?o.title.length>100&&(e.title="标题长度不能超过100个字符"):e.title="标题不能为空",o.content.trim()||(e.content="内容不能为空"),o.description&&o.description.length>200&&(e.description="描述长度不能超过200个字符"),u(e),0===Object.keys(e).length})()){d(!0);try{const t={...o,title:o.title.trim(),description:o.description.trim(),content:o.content.trim(),folderId:o.folderId||null};h&&(t.id=e.id),await n(t)}catch(e){console.error("Error saving prompt:",e),u({general:"保存失败，请重试"})}finally{d(!1)}}};return(0,_e.jsxs)(Ae,{className:"w-full max-w-2xl max-h-[90vh] overflow-hidden shadow-lg",onKeyDown:e=>{"Escape"===e.key?a():"Enter"===e.key&&(e.ctrlKey||e.metaKey)&&(e.preventDefault(),v())},children:[(0,_e.jsxs)($e,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[(0,_e.jsx)(Ve,{className:"text-lg",children:h?"编辑 Prompt":"添加 Prompt"}),(0,_e.jsx)(Pe,{variant:"ghost",size:"icon",onClick:a,className:"h-6 w-6",children:(0,_e.jsx)(p,{className:"h-4 w-4"})})]}),(0,_e.jsxs)(Be,{className:"overflow-y-auto space-y-4",children:[s.general&&(0,_e.jsx)("div",{className:"bg-destructive/10 border border-destructive/20 rounded-md p-3 text-sm text-destructive",children:s.general}),(0,_e.jsxs)("div",{className:"space-y-2",children:[(0,_e.jsxs)("label",{className:"text-sm font-medium text-foreground",htmlFor:"prompt-title",children:["标题 ",(0,_e.jsx)("span",{className:"text-destructive",children:"*"})]}),(0,_e.jsx)(Te,{id:"prompt-title",value:o.title,onChange:e=>g("title",e.target.value),placeholder:"输入 Prompt 标题",maxLength:100,className:s.title?"border-destructive":""}),s.title&&(0,_e.jsx)("div",{className:"text-sm text-destructive",children:s.title})]}),(0,_e.jsxs)("div",{className:"space-y-2",children:[(0,_e.jsx)("label",{className:"text-sm font-medium text-foreground",htmlFor:"prompt-description",children:"描述"}),(0,_e.jsx)(Te,{id:"prompt-description",value:o.description,onChange:e=>g("description",e.target.value),placeholder:"输入 Prompt 描述（可选）",maxLength:200,className:s.description?"border-destructive":""}),s.description&&(0,_e.jsx)("div",{className:"text-sm text-destructive",children:s.description})]}),(0,_e.jsxs)("div",{className:"space-y-2",children:[(0,_e.jsx)("label",{className:"text-sm font-medium text-foreground",htmlFor:"prompt-folder",children:"文件夹"}),(0,_e.jsxs)(Ye,{value:o.folderId,onChange:e=>g("folderId",e.target.value),children:[(0,_e.jsx)("option",{value:"",children:"无文件夹"}),t.map(e=>(0,_e.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,_e.jsxs)("div",{className:"space-y-2",children:[(0,_e.jsxs)("label",{className:"text-sm font-medium text-foreground",htmlFor:"prompt-content",children:["内容 ",(0,_e.jsx)("span",{className:"text-destructive",children:"*"})]}),(0,_e.jsx)(Ge,{id:"prompt-content",value:o.content,onChange:e=>g("content",e.target.value),placeholder:"输入 Prompt 内容",rows:8,className:"font-mono "+(s.content?"border-destructive":"")}),s.content&&(0,_e.jsx)("div",{className:"text-sm text-destructive",children:s.content})]}),(0,_e.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,_e.jsx)("div",{children:h&&(0,_e.jsx)(Pe,{variant:"destructive",onClick:async()=>{if(h&&e&&window.confirm("确定要删除这个 Prompt 吗？此操作不可撤销。")){m(!0);try{await l(e.id)}catch(e){console.error("Error deleting prompt:",e),u({general:"删除失败，请重试"})}finally{m(!1)}}},disabled:f,children:f?"删除中...":"删除"})}),(0,_e.jsxs)("div",{className:"flex gap-2",children:[(0,_e.jsx)(Pe,{variant:"outline",onClick:a,disabled:c||f,children:"取消"}),(0,_e.jsx)(Pe,{onClick:v,disabled:c||f,children:c?"保存中...":"保存"})]})]}),(0,_e.jsxs)("div",{className:"flex gap-4 text-xs text-muted-foreground pt-2 border-t",children:[(0,_e.jsxs)("span",{className:"flex items-center gap-1",children:[(0,_e.jsx)("kbd",{className:"px-1 py-0.5 bg-muted rounded text-xs",children:"Ctrl+Enter"}),"保存"]}),(0,_e.jsxs)("span",{className:"flex items-center gap-1",children:[(0,_e.jsx)("kbd",{className:"px-1 py-0.5 bg-muted rounded text-xs",children:"Esc"}),"取消"]})]})]})]})},Ze=c("archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]]),Je=function({folders:e,onPromptMoveToFolder:t,onFolderReorder:n}){const[a,l]=(0,r.useState)(null),[o,i]=(0,r.useState)(null),s=(e,t)=>{e.preventDefault(),e.dataTransfer.dropEffect="move",l(t)},u=(e,t)=>{const n=e.currentTarget.getBoundingClientRect(),r=e.clientX,a=e.clientY;(r<n.left||r>n.right||a<n.top||a>n.bottom)&&l(null)},c=(e,r)=>{e.preventDefault();const a=e.dataTransfer.getData("text/plain");if(a.startsWith("folder:")){const e=a.replace("folder:","");r&&e!==r.id&&n&&n(e,r.id)}else a&&t&&t(a,r?r.id:null);l(null),i(null)},d=()=>{i(null),l(null)};return(0,_e.jsxs)("div",{className:"h-full flex flex-col",children:[(0,_e.jsx)("div",{className:"p-4 border-b border-slate-200/60",children:(0,_e.jsxs)("h3",{className:"text-sm font-semibold text-slate-700 flex items-center gap-2",children:[(0,_e.jsx)(We,{className:"h-4 w-4 text-blue-500"}),"文件夹"]})}),(0,_e.jsxs)("div",{className:"flex-1 overflow-y-auto p-3",children:[(0,_e.jsxs)("div",{className:je("flex items-center p-3 mb-2 rounded-lg border cursor-pointer transition-all duration-200","bg-white/60 hover:bg-slate-50 border-slate-200/60",null===a&&"bg-gradient-to-r from-amber-50 to-yellow-50 border-amber-300 shadow-sm"),onDragOver:e=>s(e,null),onDragLeave:e=>u(e),onDrop:e=>c(e,null),children:[(0,_e.jsx)(Ze,{className:"h-4 w-4 text-slate-500 mr-3 flex-shrink-0"}),(0,_e.jsx)("span",{className:"text-sm text-slate-600 font-medium",children:"无分类"})]}),(0,_e.jsx)("div",{className:"space-y-1",children:e.sort((e,t)=>(e.order||0)-(t.order||0)).map(e=>(0,_e.jsxs)("div",{className:je("flex items-center p-3 rounded-lg border cursor-pointer transition-all duration-200 group","bg-white/60 hover:bg-blue-50/80 border-slate-200/60 hover:border-blue-200",a&&a.id===e.id&&"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-300 shadow-sm",o&&o.id===e.id&&"opacity-50 scale-95"),draggable:!0,onDragStart:t=>((e,t)=>{i(t),e.dataTransfer.effectAllowed="move",e.dataTransfer.setData("text/plain",`folder:${t.id}`)})(t,e),onDragEnd:d,onDragOver:t=>s(t,e),onDragLeave:e=>u(e),onDrop:t=>c(t,e),children:[(0,_e.jsx)(We,{className:"h-4 w-4 text-blue-500 mr-3 flex-shrink-0 group-hover:text-blue-600"}),(0,_e.jsx)("span",{className:"text-sm font-medium text-slate-700 truncate group-hover:text-blue-700",children:e.name})]},e.id))})]})]})},et=function(){const[e,t]=(0,r.useState)([]),[n,a]=(0,r.useState)([]),[l,o]=(0,r.useState)([]),[i,s]=(0,r.useState)(0),[u,c]=(0,r.useState)(""),[g,v]=(0,r.useState)(!0),[b,y]=(0,r.useState)(!1),[x,w]=(0,r.useState)(!1),[k,S]=(0,r.useState)(null),N=(0,r.useRef)(null);(0,r.useEffect)(()=>{console.log("🎨 App component mounted, checking styles..."),console.log("📊 Current state:",{prompts:e.length,folders:n.length,loading:g,searchQuery:u}),setTimeout(()=>{const e=document.querySelector(".app-container"),t=document.querySelector(".header-container"),n=document.querySelector(".brand-icon");console.log("🔍 CSS Classes Check:",{appContainer:e?"✅ Found":"❌ Missing",headerContainer:t?"✅ Found":"❌ Missing",brandIcon:n?"✅ Found":"❌ Missing",appContainerStyles:e?getComputedStyle(e).background:"N/A"})},100)},[e.length,n.length,g,u]),(0,r.useEffect)(()=>(E(),window.addEventListener("message",j),()=>{window.removeEventListener("message",j)}),[]),(0,r.useEffect)(()=>{N.current&&N.current.focus()},[]),(0,r.useEffect)(()=>{_(u)},[e,u]),(0,r.useEffect)(()=>{s(0)},[l]);const E=async()=>{try{let e,n;v(!0);try{e=await C({action:"get-prompts"}),n=await C({action:"get-folders"})}catch(t){console.log("🔧 Background script not available, using test data"),e=[{id:"1",title:"邮件开头",description:"专业的邮件问候语",content:"尊敬的[姓名]先生/女士：\n\n您好！",usageCount:5,createdAt:Date.now()-864e5,updatedAt:Date.now()-36e5,lastUsedAt:Date.now()-18e5},{id:"2",title:"会议邀请",description:"标准的会议邀请模板",content:"我们诚挚邀请您参加将于[时间]举行的[会议名称]。",usageCount:3,createdAt:Date.now()-1728e5,updatedAt:Date.now()-72e5}],n=[{id:"f1",name:"无文件夹",order:0},{id:"f2",name:"工作邮件",order:1}]}t(e||[]),a(n||[])}catch(e){console.error("Error loading data:",e)}finally{v(!1)}},C=e=>new Promise((t,n)=>{chrome.runtime.sendMessage(e,e=>{chrome.runtime.lastError?n(chrome.runtime.lastError):t(e)})}),j=e=>{"panel-shown"===e.data.action&&N.current&&setTimeout(()=>{N.current.focus()},100)},_=t=>{let r=[...e];if(r.sort((e,t)=>("number"==typeof e.order?e.order:e.createdAt||0)-("number"==typeof t.order?t.order:t.createdAt||0)),!t.trim())return void o(r);const a=t.toLowerCase();r=r.filter(e=>{var t;const r=e.title.toLowerCase().includes(a),l=null===(t=e.description)||void 0===t?void 0:t.toLowerCase().includes(a),o=e.content.toLowerCase().includes(a);let i=!1;if(e.folderId){const t=n.find(t=>t.id===e.folderId);t&&(i=t.name.toLowerCase().includes(a))}return r||l||o||i}),o(r)},z=e=>{window.parent.postMessage({action:"insert-prompt",content:e.content},"*"),P(e.id)},P=async n=>{try{const r=e.find(e=>e.id===n);if(r){const e={...r,usageCount:(r.usageCount||0)+1,lastUsedAt:Date.now()};await C({action:"save-prompt",prompt:e}),t(t=>t.map(t=>t.id===n?e:t))}}catch(e){console.error("Error updating prompt usage:",e)}},T=()=>{window.parent.postMessage({action:"hide-panel"},"*")},L=()=>{w(!1),S(null)},D=async e=>{try{const t=await C({action:"delete-prompt",promptId:e});if(!t.success)throw new Error(t.error||"删除失败");await E(),L()}catch(e){throw console.error("Error deleting prompt:",e),e}};return g?(0,_e.jsx)("div",{className:"flex items-center justify-center h-screen app-container",children:(0,_e.jsxs)("div",{className:"text-center",children:[(0,_e.jsx)("div",{className:"w-12 h-12 brand-icon rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse",children:(0,_e.jsx)("span",{className:"text-white text-lg font-bold",children:"P"})}),(0,_e.jsx)("div",{className:"text-slate-600 font-medium",children:"加载中..."})]})}):(0,_e.jsxs)("div",{className:"h-screen flex flex-col app-container text-foreground",onKeyDown:e=>{if(0!==l.length)switch(e.key){case"ArrowDown":e.preventDefault(),s(e=>e<l.length-1?e+1:0);break;case"ArrowUp":e.preventDefault(),s(e=>e>0?e-1:l.length-1);break;case"Enter":e.preventDefault(),l[i]&&z(l[i]);break;case"Escape":e.preventDefault(),T()}},tabIndex:0,children:[(0,_e.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 header-container",children:[(0,_e.jsxs)("div",{className:"flex items-center gap-3",children:[(0,_e.jsxs)("div",{className:"relative",children:[(0,_e.jsx)("div",{className:"w-9 h-9 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg",children:(0,_e.jsx)(d,{className:"h-5 w-5 text-white"})}),(0,_e.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"})]}),(0,_e.jsxs)("div",{children:[(0,_e.jsx)("h1",{className:"text-lg font-bold text-slate-800",children:"Prompt 管理器"}),(0,_e.jsx)("p",{className:"text-xs text-slate-500",children:"智能提示词管理工具"})]})]}),(0,_e.jsxs)("div",{className:"flex items-center gap-1",children:[(0,_e.jsx)(Pe,{variant:"ghost",size:"icon",onClick:()=>{y(!0)},className:"h-8 w-8 rounded-lg hover:bg-slate-100/80 hover:scale-105 transition-all duration-200 group",children:(0,_e.jsx)(f,{className:"h-4 w-4 text-slate-600 group-hover:text-slate-700 group-hover:rotate-90 transition-all duration-200"})}),(0,_e.jsx)(Pe,{variant:"ghost",size:"icon",onClick:T,className:"h-8 w-8 rounded-lg hover:bg-red-50 hover:scale-105 transition-all duration-200 group",children:(0,_e.jsx)(p,{className:"h-4 w-4 text-slate-600 group-hover:text-red-600 transition-colors duration-200"})})]})]}),(0,_e.jsx)("div",{className:"px-6 py-4 search-container",children:(0,_e.jsxs)("div",{className:"relative",children:[(0,_e.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 z-10",children:(0,_e.jsx)(m,{className:"h-4 w-4 text-slate-400"})}),(0,_e.jsx)(Te,{ref:N,value:u,onChange:e=>c(e.target.value),placeholder:"搜索 Prompt、文件夹或内容...",className:"pl-10 pr-4 h-12 bg-white/80 border-slate-200/60 rounded-xl shadow-sm focus:shadow-md focus:border-blue-300 transition-all duration-200 text-sm placeholder:text-slate-400"}),u&&(0,_e.jsx)("button",{onClick:()=>c(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-slate-100 transition-colors duration-200",children:(0,_e.jsx)(p,{className:"h-3 w-3 text-slate-400"})})]})}),(0,_e.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[(0,_e.jsx)("div",{className:"w-64 border-r border-slate-200/60 bg-white/30 overflow-y-auto",children:(0,_e.jsx)(Je,{folders:n,onPromptMoveToFolder:async(e,t)=>{try{const n=await C({action:"move-prompt-to-folder",promptId:e,folderId:t});if(!n.success)throw new Error(n.error||"移动失败");await E()}catch(e){console.error("Error moving prompt to folder:",e)}},onFolderReorder:async(e,t)=>{try{const r=[...n].sort((e,t)=>(e.order||0)-(t.order||0)),a=r.findIndex(t=>t.id===e),l=r.findIndex(e=>e.id===t);if(-1===a||-1===l)return;const o=[...r],[i]=o.splice(a,1);o.splice(l,0,i);const s=o.map(e=>e.id),u=await C({action:"reorder-folders",folderIds:s});if(!u.success)throw new Error(u.error||"排序失败");await E()}catch(e){console.error("Error reordering folders:",e)}}})}),(0,_e.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,_e.jsx)("div",{className:"px-6 py-3 border-b border-slate-200/60 bg-white/30",children:(0,_e.jsxs)(Pe,{onClick:()=>{S(null),w(!0)},className:"w-full justify-center gap-2 h-10 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-sm transition-all duration-200",children:[(0,_e.jsx)(h,{className:"h-4 w-4"}),"添加 Prompt"]})}),(0,_e.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,_e.jsx)(Ue,{prompts:l,selectedIndex:i,onSelect:s,onInsert:z,getFolderName:e=>{if(!e)return null;const t=n.find(t=>t.id===e);return t?t.name:null},searchQuery:u,onEdit:e=>{S(e),w(!0)},onDelete:D,onPromptMove:async(e,t)=>{try{const n=[...l],r=n.findIndex(t=>t.id===e.id),a=n.findIndex(e=>e.id===t.id);if(-1===r||-1===a)return;const o=[...n],[i]=o.splice(r,1);o.splice(a,0,i);const s=o.map(e=>e.id),u=await C({action:"reorder-prompts",promptIds:s});if(!u.success)throw new Error(u.error||"排序失败");await E()}catch(e){console.error("Error moving prompt:",e)}}})})]})]}),(0,_e.jsx)("div",{className:"footer-container px-6 py-3",children:(0,_e.jsxs)("div",{className:"flex gap-6 text-xs text-slate-500",children:[(0,_e.jsxs)("span",{className:"flex items-center gap-2",children:[(0,_e.jsx)("kbd",{className:"px-2 py-1 kbd-style text-xs font-mono",children:"↑↓"}),(0,_e.jsx)("span",{className:"font-medium",children:"选择"})]}),(0,_e.jsxs)("span",{className:"flex items-center gap-2",children:[(0,_e.jsx)("kbd",{className:"px-2 py-1 kbd-style text-xs font-mono",children:"Enter"}),(0,_e.jsx)("span",{className:"font-medium",children:"插入"})]}),(0,_e.jsxs)("span",{className:"flex items-center gap-2",children:[(0,_e.jsx)("kbd",{className:"px-2 py-1 kbd-style text-xs font-mono",children:"Esc"}),(0,_e.jsx)("span",{className:"font-medium",children:"关闭"})]})]})}),b&&(0,_e.jsx)("div",{className:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:(0,_e.jsx)("div",{className:"w-full max-w-4xl max-h-[90vh] overflow-hidden",children:(0,_e.jsx)(Ke,{onClose:()=>{y(!1)},onDataImported:()=>{E()}})})}),x&&(0,_e.jsx)("div",{className:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:(0,_e.jsx)("div",{className:"w-full max-w-3xl max-h-[90vh] overflow-hidden",children:(0,_e.jsx)(Xe,{prompt:k,folders:n,onSave:async e=>{try{const t=await C({action:"save-prompt",prompt:e});if(!t.success)throw new Error(t.error||"保存失败");await E(),L()}catch(e){throw console.error("Error saving prompt:",e),e}},onCancel:L,onDelete:D})})})]})};function tt(){const e=document.getElementById("root");e&&a.createRoot(e).render((0,_e.jsx)(et,{}))}"loading"===document.readyState?document.addEventListener("DOMContentLoaded",tt):tt()})();