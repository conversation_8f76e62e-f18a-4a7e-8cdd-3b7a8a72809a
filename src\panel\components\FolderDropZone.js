import React, { useState } from 'react';
import { Folder, FolderMinus } from 'lucide-react';
import { cn } from '../../lib/utils';

function FolderDropZone({ folders, onPromptMoveToFolder, onFolderReorder }) {
  const [dragOverFolder, setDragOverFolder] = useState(null);
  const [draggedFolder, setDraggedFolder] = useState(null);

  const handleDragOver = (e, folder) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverFolder(folder);
  };

  const handleDragLeave = (e, folder) => {
    // 只有当鼠标真正离开文件夹区域时才清除高亮
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;
    
    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDragOverFolder(null);
    }
  };

  const handleDrop = (e, folder) => {
    e.preventDefault();
    const draggedData = e.dataTransfer.getData('text/plain');

    // 检查是否是文件夹拖拽
    if (draggedData.startsWith('folder:')) {
      const draggedFolderId = draggedData.replace('folder:', '');
      if (folder && draggedFolderId !== folder.id && onFolderReorder) {
        onFolderReorder(draggedFolderId, folder.id);
      }
    } else {
      // Prompt拖拽
      if (draggedData && onPromptMoveToFolder) {
        onPromptMoveToFolder(draggedData, folder ? folder.id : null);
      }
    }

    setDragOverFolder(null);
    setDraggedFolder(null);
  };

  // 文件夹拖拽事件
  const handleFolderDragStart = (e, folder) => {
    setDraggedFolder(folder);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', `folder:${folder.id}`);
  };

  const handleFolderDragEnd = () => {
    setDraggedFolder(null);
    setDragOverFolder(null);
  };

  return (
    <div className="p-5">
      <div className="mb-4">
        <h4 className="text-sm font-bold text-slate-700 flex items-center gap-2">
          <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
          拖拽到文件夹
        </h4>
      </div>

      {/* 无文件夹选项 */}
      <div
        className={cn(
          "flex items-center p-3 mb-3 rounded-xl border cursor-pointer transition-all duration-200",
          "bg-white/60 hover:bg-slate-50 border-slate-200 shadow-sm",
          dragOverFolder === null && "bg-gradient-to-r from-amber-50 to-yellow-50 border-amber-200 shadow-md ring-1 ring-amber-200/50"
        )}
        onDragOver={(e) => handleDragOver(e, null)}
        onDragLeave={(e) => handleDragLeave(e, null)}
        onDrop={(e) => handleDrop(e, null)}
      >
        <FolderMinus className="h-4 w-4 text-slate-500 mr-3" />
        <span className="text-sm text-slate-600 font-medium italic">无文件夹</span>
      </div>

      {/* 文件夹列表 */}
      <div className="space-y-2">
        {folders
          .sort((a, b) => (a.order || 0) - (b.order || 0))
          .map(folder => (
          <div
            key={folder.id}
            className={cn(
              "flex items-center p-3 rounded-xl border cursor-pointer transition-all duration-200",
              "bg-white/60 hover:bg-slate-50 hover:shadow-md border-slate-200 shadow-sm",
              dragOverFolder && dragOverFolder.id === folder.id && "bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 shadow-lg ring-1 ring-blue-200/50",
              draggedFolder && draggedFolder.id === folder.id && "opacity-50 scale-95 shadow-xl"
            )}
            draggable={true}
            onDragStart={(e) => handleFolderDragStart(e, folder)}
            onDragEnd={handleFolderDragEnd}
            onDragOver={(e) => handleDragOver(e, folder)}
            onDragLeave={(e) => handleDragLeave(e, folder)}
            onDrop={(e) => handleDrop(e, folder)}
          >
            <Folder className="h-4 w-4 text-blue-500 mr-3" />
            <span className="text-sm font-semibold text-slate-700 truncate">{folder.name}</span>
          </div>
        ))}
      </div>
    </div>
  );
}

export default FolderDropZone;
